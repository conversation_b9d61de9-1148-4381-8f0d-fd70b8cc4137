const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');

/**
 * Enhanced Token Lifecycle Management System
 * Provides automatic token validation, health monitoring, and lifecycle management
 * with time-based rotation support
 */
class TokenLifecycleManager extends EventEmitter {
    constructor(config = {}) {
        super();

        this.config = {
            tokensFile: config.tokensFile || './tokens.json',
            validationInterval: config.validationInterval || 300000, // 5 minutes
            maxInvalidAttempts: config.maxInvalidAttempts || 3,
            rotationInterval: config.rotationInterval || 3600000, // 1 hour
            healthCheckInterval: config.healthCheckInterval || 60000, // 1 minute
            lockTimeout: config.lockTimeout || 30000,
            instanceId: config.instanceId || `token_manager_${process.pid}_${crypto.randomBytes(4).toString('hex')}`,
            // Time-based rotation configuration
            timeBasedRotation: {
                enabled: true,
                intervalMinutes: 30,
                strategy: 'time_based',
                maxTokenUsageTime: 1800000,
                rotationGracePeriod: 60000,
                ...config.timeBasedRotation
            },
            ...config
        };

        this.logger = config.logger;
        this.errorManager = config.errorManager;

        // Debug configuration
        this.logger?.info('🔧 TokenLifecycleManager configuration', {
            environment: this.config.environment,
            tokensFile: this.config.tokensFile,
            instanceId: this.config.instanceId,
            note: 'JWT expiration checks disabled'
        });

        // Token state management
        this.tokens = new Map();
        this.tokenUsage = new Map();
        this.tokenHealth = new Map();
        this.assignedTokens = new Map();
        this.tokenAssignmentTimes = new Map(); // Track when tokens were assigned

        // Timers and intervals
        this.validationTimer = null;
        this.healthCheckTimer = null;
        this.rotationTimer = null;
        this.timeBasedRotationTimer = null;
        
        // File locking
        this.lockFile = `${this.config.tokensFile}.lock`;
        
        this.initialize();
    }

    /**
     * Initialize token lifecycle manager
     */
    async initialize() {
        try {
            await this.loadTokens();
            // Skip initial validation - assume all tokens are valid at startup
            // as they may have been updated by external scripts
            await this.markAllTokensValidAtStartup();
            this.startPeriodicTasks();

            // Start time-based rotation if enabled
            if (this.config.timeBasedRotation.enabled) {
                this.startTimeBasedRotation();
            }

            this.logger?.info('✅ Token Lifecycle Manager initialized', {
                totalTokens: this.tokens.size,
                validTokens: this.getValidTokenCount(),
                note: 'All tokens marked as valid at startup'
            });
        } catch (error) {
            this.logger?.error('❌ Failed to initialize Token Lifecycle Manager', { error: error.message });
            throw error;
        }
    }

    /**
     * Load tokens from file with enhanced validation
     */
    async loadTokens() {
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for token loading');
        }

        try {
            if (!fs.existsSync(this.config.tokensFile)) {
                throw new Error(`Token file not found: ${this.config.tokensFile}`);
            }

            const data = fs.readFileSync(this.config.tokensFile, 'utf8');
            const tokenData = JSON.parse(data);
            
            this.tokens.clear();
            this.tokenHealth.clear();
            this.tokenUsage.clear();

            const tokens = tokenData.tokens || [];
            for (let i = 0; i < tokens.length; i++) {
                const token = tokens[i];
                const tokenId = token.id || i + 1;
                
                const enhancedToken = {
                    id: tokenId,
                    token: token.token,
                    phone: token.phone || `phone_${tokenId}`, // Default phone if not provided
                    isValid: token.isValid !== undefined ? token.isValid : true, // Preserve file state, default to true only if undefined
                    lastValidated: token.lastValidated ? new Date(token.lastValidated) : null,
                    registeredAt: token.registeredAt ? new Date(token.registeredAt) : new Date(),
                    invalidAttempts: token.invalidAttempts || 0, // Preserve invalid attempts from file
                    bannedUntil: token.bannedUntil ? new Date(token.bannedUntil) : null, // Preserve ban status from file
                    assignedTo: token.assignedTo || null,
                    metadata: token.metadata || {},
                    invalidReason: token.invalidReason || null // Preserve invalid reason from file
                };

                // Skip JWT parsing - tokens are managed externally
                enhancedToken.jwtPayload = null;
                enhancedToken.expiresAt = null;

                this.tokens.set(tokenId, enhancedToken);
                this.initializeTokenHealth(tokenId);
                this.initializeTokenUsage(tokenId);
            }

            this.logger?.info('📥 Tokens loaded successfully', {
                totalTokens: this.tokens.size,
                validTokens: this.getValidTokenCount()
            });

        } finally {
            this.releaseLock();
        }
    }



    /**
     * Initialize token health tracking
     */
    initializeTokenHealth(tokenId) {
        this.tokenHealth.set(tokenId, {
            successCount: 0,
            failureCount: 0,
            lastSuccess: null,
            lastFailure: null,
            responseTime: [],
            healthScore: 1.0,
            status: 'unknown' // unknown, healthy, degraded, unhealthy
        });
    }

    /**
     * Initialize token usage tracking
     */
    initializeTokenUsage(tokenId) {
        this.tokenUsage.set(tokenId, {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            lastUsed: null,
            averageResponseTime: 0,
            requestsPerHour: 0,
            usageHistory: []
        });
    }

    /**
     * Mark all tokens as valid at startup
     * This assumes tokens may have been updated by external scripts
     * But preserves explicitly invalid tokens from the file
     */
    async markAllTokensValidAtStartup() {
        let markedValidCount = 0;
        let preservedInvalidCount = 0;

        for (const [tokenId, token] of this.tokens) {
            // Only mark as valid if not explicitly marked as invalid in the file
            if (token.isValid !== false) {
                token.isValid = true;
                // Only reset these if token was not explicitly invalid
                if (token.invalidAttempts === 0) {
                    token.invalidReason = null;
                }
                if (!token.bannedUntil) {
                    token.lastValidated = new Date();
                }

                this.updateTokenHealth(tokenId, true);
                markedValidCount++;
            } else {
                // Token was explicitly marked as invalid in file, preserve this state
                preservedInvalidCount++;
                this.updateTokenHealth(tokenId, false);
            }
        }

        this.logger?.info('🔄 Startup token validation completed', {
            markedValid: markedValidCount,
            preservedInvalid: preservedInvalidCount,
            total: this.tokens.size,
            note: 'Valid tokens marked as valid, invalid tokens preserved from file (JWT parsing disabled)'
        });
    }

    /**
     * Validate all tokens
     */
    async validateAllTokens() {
        const validationPromises = [];

        for (const [tokenId, token] of this.tokens) {
            if (this.shouldValidateToken(token)) {
                validationPromises.push(this.validateToken(tokenId));
            }
        }

        const results = await Promise.allSettled(validationPromises);

        let validatedCount = 0;
        let invalidatedCount = 0;

        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                if (result.value) {
                    validatedCount++;
                } else {
                    invalidatedCount++;
                }
            }
        });

        this.logger?.info('🔍 Token validation completed', {
            validated: validatedCount,
            invalidated: invalidatedCount,
            total: this.tokens.size
        });
    }

    /**
     * Check if token should be validated
     */
    shouldValidateToken(token) {
        // Always validate if never validated
        if (!token.lastValidated) return true;
        
        // Validate if last validation was too long ago
        const timeSinceValidation = Date.now() - token.lastValidated.getTime();
        if (timeSinceValidation > this.config.validationInterval) return true;
        

        
        // Validate if token has too many invalid attempts
        if (token.invalidAttempts >= this.config.maxInvalidAttempts) return true;
        
        return false;
    }

    /**
     * Validate individual token
     */
    async validateToken(tokenId) {
        const token = this.tokens.get(tokenId);
        if (!token) return false;

        try {
            // Skip JWT expiration checks - tokens are managed externally
            this.logger?.debug(`🔧 Token validation for token ${tokenId} (JWT expiration checks disabled)`);

            // Check if token is banned
            if (token.bannedUntil && token.bannedUntil > new Date()) {
                this.logger?.debug(`Token ${tokenId} is banned until ${token.bannedUntil}`);
                return false;
            }

            // Perform actual validation (this would be implementation-specific)
            const isValid = await this.performTokenValidation(token);
            
            if (isValid) {
                this.markTokenValid(tokenId);
                return true;
            } else {
                this.markTokenInvalid(tokenId, 'Validation failed');
                return false;
            }

        } catch (error) {
            this.logger?.error(`❌ Token validation failed for ${tokenId}`, { error: error.message });
            this.markTokenInvalid(tokenId, error.message);
            return false;
        }
    }

    /**
     * Perform actual token validation (implementation-specific)
     */
    async performTokenValidation(token) {
        this.logger?.info(`🔧 Starting token validation for token ${token.id}`);
        // This would be implemented based on your specific validation requirements
        // For now, we'll do basic checks

        // Skip JWT structure and expiration checks - tokens are managed externally
        this.logger?.debug(`🔧 Token validation for ${token.id} (JWT checks disabled)`);

        // Additional validation logic would go here
        // For example, making a test API call with the token

        return true;
    }

    /**
     * Mark token as valid
     */
    markTokenValid(tokenId) {
        const token = this.tokens.get(tokenId);
        if (token) {
            token.isValid = true;
            token.lastValidated = new Date();
            token.invalidAttempts = 0;
            token.invalidReason = null;
            
            this.updateTokenHealth(tokenId, true);
            this.logger?.debug(`✅ Token ${tokenId} marked as valid`);
        }
    }

    /**
     * Mark token as invalid (with retry logic)
     */
    markTokenInvalid(tokenId, reason) {
        const token = this.tokens.get(tokenId);
        if (token) {
            token.isValid = false;
            token.invalidAttempts = (token.invalidAttempts || 0) + 1;
            token.invalidReason = reason;
            token.lastValidated = new Date();

            // Ban token if too many invalid attempts
            if (token.invalidAttempts >= this.config.maxInvalidAttempts) {
                token.bannedUntil = new Date(Date.now() + 3600000); // Ban for 1 hour
                this.logger?.warn(`🚫 Token ${tokenId} banned due to repeated failures`);
            }

            this.updateTokenHealth(tokenId, false);
            this.logger?.debug(`❌ Token ${tokenId} marked as invalid: ${reason}`);
        }
    }

    /**
     * Mark token as permanently invalid (like original system)
     * This matches the behavior of the original TokenManager.markTokenInvalid
     */
    markTokenPermanentlyInvalid(tokenId, reason) {
        const token = this.tokens.get(tokenId);
        if (token) {
            this.logger?.warn(`🚫 Token ${tokenId} marked as permanently invalid: ${reason}`);
            token.isValid = false;
            token.invalidReason = reason;
            token.lastValidated = new Date();
            token.assignedTo = null; // Release assignment

            // Don't use retry logic for permanent invalidation
            token.invalidAttempts = this.config.maxInvalidAttempts; // Set to max to prevent reuse
            token.bannedUntil = new Date(Date.now() + 86400000); // Ban for 24 hours

            this.updateTokenHealth(tokenId, false);
        }
    }

    /**
     * Update token health metrics
     */
    updateTokenHealth(tokenId, success, responseTime = null) {
        const health = this.tokenHealth.get(tokenId);
        if (!health) return;

        if (success) {
            health.successCount++;
            health.lastSuccess = new Date();
        } else {
            health.failureCount++;
            health.lastFailure = new Date();
        }

        if (responseTime !== null) {
            health.responseTime.push(responseTime);
            if (health.responseTime.length > 100) {
                health.responseTime = health.responseTime.slice(-100);
            }
        }

        // Calculate health score
        const totalRequests = health.successCount + health.failureCount;
        if (totalRequests > 0) {
            health.healthScore = health.successCount / totalRequests;
        }

        // Determine health status
        if (health.healthScore >= 0.9) {
            health.status = 'healthy';
        } else if (health.healthScore >= 0.7) {
            health.status = 'degraded';
        } else {
            health.status = 'unhealthy';
        }
    }

    /**
     * Get available token for assignment
     */
    getAvailableToken() {
        const availableTokens = [];
        
        for (const [tokenId, token] of this.tokens) {
            if (this.isTokenAvailable(token)) {
                const health = this.tokenHealth.get(tokenId);
                availableTokens.push({
                    tokenId,
                    token,
                    health: health.healthScore,
                    usage: this.tokenUsage.get(tokenId)
                });
            }
        }

        if (availableTokens.length === 0) {
            return null;
        }

        // Sort by health score and usage (prefer healthy, less-used tokens)
        availableTokens.sort((a, b) => {
            const healthDiff = b.health - a.health;
            if (Math.abs(healthDiff) > 0.1) return healthDiff;
            return a.usage.totalRequests - b.usage.totalRequests;
        });

        return availableTokens[0];
    }

    /**
     * Check if token is available for use
     */
    isTokenAvailable(token) {
        if (!token.isValid) return false;
        if (token.bannedUntil && token.bannedUntil > new Date()) return false;
        if (token.assignedTo && token.assignedTo !== this.config.instanceId) return false;

        // Skip expiration checks - tokens are managed externally

        return true;
    }

    /**
     * Assign token to instance
     */
    assignToken(tokenId, instanceId = null) {
        const token = this.tokens.get(tokenId);
        if (!token) return false;

        const targetInstanceId = instanceId || this.config.instanceId;
        token.assignedTo = targetInstanceId;
        this.assignedTokens.set(tokenId, targetInstanceId);

        // Track assignment time for time-based rotation
        this.tokenAssignmentTimes.set(tokenId, Date.now());

        this.logger?.debug(`🔗 Token ${tokenId} assigned to ${targetInstanceId}`);
        return true;
    }

    /**
     * Release token from instance
     */
    releaseToken(tokenId) {
        const token = this.tokens.get(tokenId);
        if (!token) return false;

        token.assignedTo = null;
        this.assignedTokens.delete(tokenId);

        // Clean up assignment time tracking
        this.tokenAssignmentTimes.delete(tokenId);

        this.logger?.debug(`🔓 Token ${tokenId} released`);
        return true;
    }

    /**
     * Get count of valid tokens
     */
    getValidTokenCount() {
        let count = 0;
        for (const token of this.tokens.values()) {
            if (token.isValid && this.isTokenAvailable(token)) {
                count++;
            }
        }
        return count;
    }

    /**
     * Check if there are any available tokens
     */
    hasAvailableTokens() {
        return this.getValidTokenCount() > 0;
    }

    /**
     * Get detailed token status for debugging
     */
    getTokenStatus() {
        const status = {
            totalTokens: this.tokens.size,
            validTokens: 0,
            invalidTokens: 0,
            bannedTokens: 0,
            assignedTokens: 0,
            availableTokens: 0
        };

        for (const token of this.tokens.values()) {
            if (token.isValid) {
                status.validTokens++;
                if (this.isTokenAvailable(token)) {
                    status.availableTokens++;
                } else if (token.assignedTo) {
                    status.assignedTokens++;
                }
            } else {
                status.invalidTokens++;
            }

            if (token.bannedUntil && token.bannedUntil > new Date()) {
                status.bannedTokens++;
            }
        }

        return status;
    }

    /**
     * Start periodic tasks
     */
    startPeriodicTasks() {
        // Periodic validation
        this.validationTimer = setInterval(() => {
            this.validateAllTokens().catch(error => {
                this.logger?.error('❌ Periodic token validation failed', { error: error.message });
            });
        }, this.config.validationInterval);

        // Health checks
        this.healthCheckTimer = setInterval(() => {
            this.performHealthChecks();
        }, this.config.healthCheckInterval);

        // Token rotation
        this.rotationTimer = setInterval(() => {
            this.rotateTokens();
        }, this.config.rotationInterval);
    }

    /**
     * Stop periodic tasks
     */
    stopPeriodicTasks() {
        if (this.validationTimer) {
            clearInterval(this.validationTimer);
            this.validationTimer = null;
        }

        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }

        if (this.rotationTimer) {
            clearInterval(this.rotationTimer);
            this.rotationTimer = null;
        }

        // Stop time-based rotation
        this.stopTimeBasedRotation();

        this.logger?.debug('🛑 Periodic tasks stopped');
    }

    /**
     * Perform health checks on all tokens
     */
    performHealthChecks() {
        for (const [tokenId, token] of this.tokens) {
            if (token.isValid) {
                // Check if token hasn't been used recently and might be stale
                const usage = this.tokenUsage.get(tokenId);
                if (usage.lastUsed && Date.now() - usage.lastUsed.getTime() > 3600000) {
                    // Token hasn't been used in an hour, validate it
                    this.validateToken(tokenId).catch(error => {
                        this.logger?.debug(`Health check validation failed for token ${tokenId}`, { error: error.message });
                    });
                }
            }
        }
    }

    /**
     * Rotate tokens to distribute load
     */
    rotateTokens() {
        // Release tokens that have been assigned for too long
        for (const [tokenId, instanceId] of this.assignedTokens) {
            const token = this.tokens.get(tokenId);
            if (token && token.assignedTo === instanceId) {
                const usage = this.tokenUsage.get(tokenId);
                if (usage.lastUsed && Date.now() - usage.lastUsed.getTime() > this.config.rotationInterval) {
                    this.releaseToken(tokenId);
                    this.logger?.debug(`🔄 Token ${tokenId} rotated due to long assignment`);
                }
            }
        }
    }

    /**
     * Start time-based token rotation
     */
    startTimeBasedRotation() {
        if (this.timeBasedRotationTimer) {
            clearInterval(this.timeBasedRotationTimer);
        }

        const intervalMs = this.config.timeBasedRotation.intervalMinutes * 60 * 1000;

        this.timeBasedRotationTimer = setInterval(() => {
            this.checkTimeBasedRotation();
        }, intervalMs);

        this.logger?.info('⏰ Time-based token rotation started', {
            intervalMinutes: this.config.timeBasedRotation.intervalMinutes,
            strategy: this.config.timeBasedRotation.strategy
        });
    }

    /**
     * Stop time-based token rotation
     */
    stopTimeBasedRotation() {
        if (this.timeBasedRotationTimer) {
            clearInterval(this.timeBasedRotationTimer);
            this.timeBasedRotationTimer = null;
            this.logger?.info('⏰ Time-based token rotation stopped');
        }
    }

    /**
     * Check if time-based rotation is needed
     */
    checkTimeBasedRotation() {
        const now = Date.now();
        const rotationsNeeded = [];

        for (const [tokenId, assignmentTime] of this.tokenAssignmentTimes) {
            const token = this.tokens.get(tokenId);
            if (!token || !token.assignedTo) {
                // Token is not assigned, remove from tracking
                this.tokenAssignmentTimes.delete(tokenId);
                continue;
            }

            const usageTime = now - assignmentTime;
            const maxUsageTime = this.config.timeBasedRotation.maxTokenUsageTime;

            if (usageTime >= maxUsageTime) {
                rotationsNeeded.push({
                    tokenId,
                    usageTime,
                    assignedTo: token.assignedTo
                });
            }
        }

        if (rotationsNeeded.length > 0) {
            this.logger?.info(`🔄 Time-based rotation needed for ${rotationsNeeded.length} tokens`, {
                tokens: rotationsNeeded.map(r => ({ tokenId: r.tokenId, usageTimeMinutes: Math.round(r.usageTime / 60000) }))
            });

            for (const rotation of rotationsNeeded) {
                this.forceTokenRotation(rotation.tokenId, 'time_based_rotation');
            }
        }
    }

    /**
     * Force token rotation for a specific token
     */
    forceTokenRotation(tokenId, reason = 'manual_rotation') {
        const token = this.tokens.get(tokenId);
        if (!token) {
            this.logger?.warn(`⚠️ Cannot rotate token ${tokenId}: token not found`);
            return false;
        }

        const assignedTo = token.assignedTo;
        if (!assignedTo) {
            this.logger?.debug(`🔄 Token ${tokenId} is not assigned, skipping rotation`);
            return false;
        }

        this.logger?.info(`🔄 Forcing token rotation`, {
            tokenId,
            reason,
            assignedTo,
            usageTime: this.tokenAssignmentTimes.has(tokenId) ?
                Math.round((Date.now() - this.tokenAssignmentTimes.get(tokenId)) / 60000) : 'unknown'
        });

        // Emit rotation event before releasing token
        this.emit('tokenRotationNeeded', {
            tokenId,
            reason,
            assignedTo,
            timestamp: new Date()
        });

        // Release the token
        this.releaseToken(tokenId);

        return true;
    }

    /**
     * Get next available token for continuous rotation
     * Excludes recently failed tokens and prioritizes healthy tokens
     */
    getNextAvailableToken(excludeTokenIds = []) {
        const availableTokens = [];

        for (const [tokenId, token] of this.tokens) {
            // Skip excluded tokens
            if (excludeTokenIds.includes(tokenId)) {
                continue;
            }

            if (this.isTokenAvailable(token)) {
                const health = this.tokenHealth.get(tokenId);
                const usage = this.tokenUsage.get(tokenId);

                availableTokens.push({
                    tokenId,
                    token,
                    health: health.healthScore,
                    usage: usage,
                    lastFailure: health.lastFailure,
                    failureCount: health.failureCount
                });
            }
        }

        if (availableTokens.length === 0) {
            return null;
        }

        // Sort by health score, failure count, and usage
        availableTokens.sort((a, b) => {
            // Prioritize tokens with higher health scores
            const healthDiff = b.health - a.health;
            if (Math.abs(healthDiff) > 0.1) return healthDiff;

            // Then prioritize tokens with fewer failures
            const failureDiff = a.failureCount - b.failureCount;
            if (failureDiff !== 0) return failureDiff;

            // Finally prioritize less-used tokens
            return a.usage.totalRequests - b.usage.totalRequests;
        });

        return availableTokens[0];
    }

    /**
     * Continuous token rotation until a valid token is found
     * Used when tokens are being banned and need to keep trying
     */
    async continuousTokenRotation(instanceId, maxAttempts = 10, excludeTokenIds = []) {
        let attempts = 0;
        const failedTokens = [...excludeTokenIds];

        this.logger?.info(`🔄 Starting continuous token rotation`, {
            instanceId,
            maxAttempts,
            excludedTokens: failedTokens.length
        });

        while (attempts < maxAttempts) {
            attempts++;

            // Get next available token
            const nextTokenData = this.getNextAvailableToken(failedTokens);

            if (!nextTokenData) {
                this.logger?.error(`❌ No more available tokens for rotation`, {
                    attempts,
                    failedTokens: failedTokens.length,
                    totalTokens: this.tokens.size
                });
                return null;
            }

            const { tokenId, token } = nextTokenData;

            this.logger?.info(`🔄 Attempting token rotation (${attempts}/${maxAttempts})`, {
                tokenId,
                instanceId,
                healthScore: nextTokenData.health
            });

            // Assign the new token
            if (this.assignToken(tokenId, instanceId)) {
                this.logger?.info(`✅ Token rotation successful`, {
                    tokenId,
                    attempts,
                    instanceId
                });

                return {
                    tokenId,
                    token,
                    attempts,
                    excludedTokens: failedTokens
                };
            } else {
                // If assignment failed, add to failed list and try next
                failedTokens.push(tokenId);
                this.logger?.warn(`⚠️ Token assignment failed, trying next token`, {
                    failedTokenId: tokenId,
                    attempts
                });
            }
        }

        this.logger?.error(`❌ Continuous token rotation failed after ${maxAttempts} attempts`, {
            instanceId,
            failedTokens: failedTokens.length
        });

        return null;
    }

    /**
     * Save tokens to file
     */
    async saveTokens() {
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for saving tokens');
        }

        try {
            const tokenArray = Array.from(this.tokens.values()).map(token => ({
                id: token.id,
                token: token.token,
                phone: token.phone,
                isValid: token.isValid,
                lastValidated: token.lastValidated?.toISOString(),
                registeredAt: token.registeredAt?.toISOString(),
                invalidAttempts: token.invalidAttempts,
                bannedUntil: token.bannedUntil?.toISOString(),
                assignedTo: token.assignedTo,
                invalidReason: token.invalidReason,
                metadata: token.metadata
            }));

            const data = { tokens: tokenArray };
            fs.writeFileSync(this.config.tokensFile, JSON.stringify(data, null, 2));
            
            this.logger?.debug('💾 Tokens saved to file');
        } finally {
            this.releaseLock();
        }
    }

    /**
     * Acquire file lock
     */
    async acquireLock() {
        const startTime = Date.now();
        let waitTime = 50;
        const maxWaitTime = 500;

        while (Date.now() - startTime < this.config.lockTimeout) {
            try {
                if (fs.existsSync(this.lockFile)) {
                    const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                    if (Date.now() - lockData.timestamp > this.config.lockTimeout) {
                        fs.unlinkSync(this.lockFile);
                    }
                }
                
                const lockData = { instanceId: this.config.instanceId, timestamp: Date.now() };
                fs.writeFileSync(this.lockFile, JSON.stringify(lockData), { flag: 'wx' });
                return true;
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    this.logger?.error('Lock acquisition error', { error: error.message });
                }
                
                await new Promise(resolve => setTimeout(resolve, waitTime));
                waitTime = Math.min(waitTime * 2, maxWaitTime);
            }
        }
        
        return false;
    }

    /**
     * Release file lock
     */
    releaseLock() {
        try {
            if (fs.existsSync(this.lockFile)) {
                const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                if (lockData.instanceId === this.config.instanceId) {
                    fs.unlinkSync(this.lockFile);
                }
            }
        } catch (error) {
            this.logger?.warn('Lock release error', { error: error.message });
        }
    }

    /**
     * Get token statistics
     */
    getStatistics() {
        const stats = {
            totalTokens: this.tokens.size,
            validTokens: 0,
            invalidTokens: 0,
            bannedTokens: 0,
            assignedTokens: this.assignedTokens.size,
            healthyTokens: 0,
            degradedTokens: 0,
            unhealthyTokens: 0
        };

        for (const token of this.tokens.values()) {
            if (token.isValid) {
                stats.validTokens++;
            } else {
                stats.invalidTokens++;
            }

            if (token.bannedUntil && token.bannedUntil > new Date()) {
                stats.bannedTokens++;
            }
        }

        for (const health of this.tokenHealth.values()) {
            if (health.status === 'healthy') stats.healthyTokens++;
            else if (health.status === 'degraded') stats.degradedTokens++;
            else if (health.status === 'unhealthy') stats.unhealthyTokens++;
        }

        return stats;
    }

    /**
     * Record token usage
     */
    recordTokenUsage(tokenId, success, responseTime = null) {
        const usage = this.tokenUsage.get(tokenId);
        if (!usage) return;

        usage.totalRequests++;
        usage.lastUsed = new Date();

        if (success) {
            usage.successfulRequests++;
        } else {
            usage.failedRequests++;
        }

        if (responseTime !== null) {
            const history = usage.usageHistory;
            history.push({ timestamp: new Date(), responseTime, success });

            // Keep only last 100 entries
            if (history.length > 100) {
                usage.usageHistory = history.slice(-100);
            }

            // Calculate average response time
            const responseTimes = history.map(h => h.responseTime).filter(rt => rt !== null);
            if (responseTimes.length > 0) {
                usage.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            }
        }

        // Calculate requests per hour
        const oneHourAgo = new Date(Date.now() - 3600000);
        const recentRequests = usage.usageHistory.filter(h => h.timestamp > oneHourAgo);
        usage.requestsPerHour = recentRequests.length;

        // Update health metrics
        this.updateTokenHealth(tokenId, success, responseTime);
    }

    /**
     * Cleanup and shutdown
     */
    async shutdown() {
        // Clear timers
        if (this.validationTimer) clearInterval(this.validationTimer);
        if (this.healthCheckTimer) clearInterval(this.healthCheckTimer);
        if (this.rotationTimer) clearInterval(this.rotationTimer);
        if (this.timeBasedRotationTimer) clearInterval(this.timeBasedRotationTimer);

        // Save current state
        try {
            await this.saveTokens();
        } catch (error) {
            this.logger?.error('❌ Failed to save tokens during shutdown', { error: error.message });
        }

        // Release all assigned tokens
        for (const tokenId of this.assignedTokens.keys()) {
            this.releaseToken(tokenId);
        }

        this.logger?.info('🔄 Token Lifecycle Manager shutdown completed');
    }
}

module.exports = TokenLifecycleManager;
