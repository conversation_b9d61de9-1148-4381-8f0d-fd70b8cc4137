const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const HumanBehaviorSimulator = require('../utils/HumanBehaviorSimulator');
const { TokenExhaustedException } = require('../core/ErrorManager');
const https = require('https');
const http = require('http');
const { URL } = require('url');

/**
 * Enhanced Playwright Crawler with comprehensive error handling and state management
 */
class EnhancedPlaywrightCrawler {
    constructor(config = {}) {
        this.config = {
            headless: config.headless !== false,
            timeout: config.timeout || 100000,
            pageTimeout: config.pageTimeout || 100000,
            maxInstances: config.maxInstances || 5,
            minDelay: config.minDelay || 1000,
            maxDelay: config.maxDelay || 3000,
            maxRetries: config.maxRetries || 3,
            retryDelay: config.retryDelay || 5000,
            requestTimeout: config.requestTimeout || 30000,
            outputDir: config.outputDir || './output',
            paramsFile: config.paramsFile || './params.json',
            userAgent: config.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            // 代理配置格式：
            // proxy: {
            //   enabled: true,
            //   protocol: 'http',
            //   host: 'proxy-server.com',
            //   port: 8080,
            //   auth: {
            //     username: '代理账号',
            //     password: '代理密码'
            //   }
            // }
            proxy: config.proxy || null,
            proxyUrl: config.proxyUrl || null,
            // Browser cache management configuration
            browserCache: config.browserCache || {
                clearOnTokenRotation: true,
                clearCache: true,
                clearCookies: true,
                clearLocalStorage: true,
                clearSessionStorage: true,
                clearIndexedDB: true,
                validateTokenAfterRotation: true,
                rotationTimeout: 30000
            },
            ...config
        };

        this.logger = config.logger;
        this.errorManager = config.errorManager;
        this.tokenManager = config.tokenManager;
        this.feishuNotifier = config.feishuNotifier;

        // Enhanced logger with token information
        this.enhancedLogger = this.createEnhancedLogger();

        // Browser management
        this.browser = null;
        this.contexts = new Map();
        this.pages = new Map();
        this.activeRequests = new Set();

        // Proxy management
        this.currentProxy = null;
        this.proxyPool = null;
        this.proxyHealthCheckInterval = null;

        // State tracking
        this.isInitialized = false;
        this.isShuttingDown = false;
        this.requestCount = 0;
        this.successCount = 0;

        // Token rotation handling
        this.setupTokenRotationListener();

        // 初始化人类行为模拟器
        this.humanBehavior = new HumanBehaviorSimulator({
            logger: this.logger,
            enableAntiDetection: this.config.enableAntiDetection !== false,
            minDelay: this.config.minDelay || 100,
            maxDelay: this.config.maxDelay || 500
        });
        this.errorCount = 0;

        // Performance metrics
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            responseTimes: []
        };
    }

    /**
     * Initialize the crawler
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            this.enhancedLogger?.info('🚀 正在初始化增强版Playwright爬虫');

            // Ensure output directory exists
            if (!fs.existsSync(this.config.outputDir)) {
                fs.mkdirSync(this.config.outputDir, { recursive: true });
            }

            // Initialize proxy configuration
            this.initializeProxyConfiguration();

            // Launch browser
            await this.launchBrowser();

            this.isInitialized = true;
            this.enhancedLogger?.info('✅ 增强版Playwright爬虫初始化完成');

        } catch (error) {
            const enhancedError = this.errorManager?.createEnhancedError(error, {
                operation: 'crawler_initialization'
            }) || error;

            this.enhancedLogger?.error('❌ 爬虫初始化失败', { error: enhancedError.message });
            throw enhancedError;
        }
    }

    /**
     * Create enhanced logger with token information
     */
    createEnhancedLogger() {
        if (!this.logger) return null;

        return {
            info: (message, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                this.logger.info(message, { ...data, ...tokenInfo });
            },
            debug: (message, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                this.logger.debug(message, { ...data, ...tokenInfo });
            },
            warn: (message, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                this.logger.warn(message, { ...data, ...tokenInfo });
            },
            error: (message, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                this.logger.error(message, { ...data, ...tokenInfo });
            },
            startOperation: (operation, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                return this.logger.startOperation ? this.logger.startOperation(operation, { ...data, ...tokenInfo }) : null;
            },
            completeOperation: (operation, operationId, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                return this.logger.completeOperation ? this.logger.completeOperation(operation, operationId, { ...data, ...tokenInfo }) : null;
            },
            failOperation: (operation, operationId, error, data = {}) => {
                const tokenInfo = this.getCurrentTokenInfo();
                return this.logger.failOperation ? this.logger.failOperation(operation, operationId, error, { ...data, ...tokenInfo }) : null;
            }
        };
    }

    /**
     * Get current token information for logging
     */
    getCurrentTokenInfo() {
        if (this.currentToken) {
            return {
                currentTokenId: this.currentToken.id,
                tokenPhone: this.currentToken.phone || 'unknown'
            };
        }
        return {
            currentTokenId: 'none',
            tokenPhone: 'none'
        };
    }

    /**
     * Initialize proxy configuration (直接配置，参考 config.js 模式)
     */
    initializeProxyConfiguration() {
        if (this.config.proxy && this.config.proxy.enabled) {
            this.enhancedLogger?.info('🔧 正在初始化代理配置', {
                host: this.config.proxy.host,
                port: this.config.proxy.port,
                protocol: this.config.proxy.protocol
            });

            // 设置当前代理
            this.currentProxy = {
                server: `${this.config.proxy.protocol || 'http'}://${this.config.proxy.host}:${this.config.proxy.port}`
            };

            // 添加代理认证信息（如果配置了的话）
            if (this.config.proxy.auth?.username && this.config.proxy.auth?.password) {
                this.currentProxy.username = this.config.proxy.auth.username;
                this.currentProxy.password = this.config.proxy.auth.password;
                
                this.enhancedLogger?.info('✅ 代理配置已初始化（含认证）', {
                    server: this.currentProxy.server,
                    hasAuth: true,
                    username: this.currentProxy.username
                });
            } else {
                this.enhancedLogger?.info('✅ 代理配置已初始化（无认证）', {
                    server: this.currentProxy.server,
                    hasAuth: false
                });
            }
        } else {
            this.enhancedLogger?.info('⚠️ 未配置代理，将使用直连模式');
        }
    }

    /**
     * Launch browser with enhanced configuration
     */
    async launchBrowser() {
        const launchOptions = {
            headless: this.config.headless,
            timeout: this.config.timeout,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        };

        this.browser = await chromium.launch(launchOptions);

        this.enhancedLogger?.info('🌐 浏览器已启动', {
            headless: this.config.headless,
            version: await this.browser.version()
        });

        // Setup browser event handlers
        this.browser.on('disconnected', () => {
            this.enhancedLogger?.warn('🔌 浏览器已断开连接');
            this.browser = null;
        });
    }

    /**
     * Create new browser context with enhanced settings
     */
    async createContext(contextId = null) {
        if (!this.browser) {
            await this.launchBrowser();
        }

        const actualContextId = contextId || `context_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        // 构建上下文配置，包含代理设置
        const contextOptions = {
            userAgent: this.config.userAgent,
            viewport: { width: 1440, height: 720 },
            ignoreHTTPSErrors: true,
            timeout: this.config.pageTimeout,
            extraHTTPHeaders: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        };

        // 添加代理配置
        if (this.currentProxy) {
            contextOptions.proxy = this.currentProxy;
            this.logger?.debug('🔗 为上下文配置代理', {
                server: this.currentProxy.server,
                hasAuth: !!(this.currentProxy.username && this.currentProxy.password),
                username: this.currentProxy.username || '无认证'
            });
        }

        const context = await this.browser.newContext(contextOptions);

        // Setup context event handlers
        context.on('page', (page) => {
            this.setupPageHandlers(page);
        });

        this.contexts.set(actualContextId, context);

        this.logger?.debug('📄 浏览器上下文已创建', { contextId: actualContextId });
        return { context, contextId: actualContextId };
    }

    /**
     * Create browser context with token injection (following original PlaywrightCrawler pattern)
     */
    async createContextWithToken(tokenData) {
        if (!this.browser) {
            await this.launchBrowser();
        }

        const contextId = `context_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        // Create context with enhanced settings and proxy support
        const contextOptions = {
            userAgent: this.config.userAgent,
            viewport: { width: 1920, height: 1080 },
            ignoreHTTPSErrors: true,
            timeout: this.config.pageTimeout,
            javaScriptEnabled: true,
            locale: 'zh-CN',
            timezoneId: 'Asia/Shanghai',
            extraHTTPHeaders: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        };

        // 添加代理配置
        if (this.currentProxy) {
            contextOptions.proxy = this.currentProxy;
            this.logger?.debug('🔗 为Token上下文配置代理', {
                server: this.currentProxy.server,
                hasAuth: !!(this.currentProxy.username && this.currentProxy.password),
                username: this.currentProxy.username || '无认证',
                tokenId: tokenData.id
            });
        }

        const context = await this.browser.newContext(contextOptions);

        // Inject token cookies (following original pattern)
        const tokenValue = tokenData.token || tokenData.id || 'mock_token';
        await context.addCookies([
            {
                name: 'ACCESS_TOKEN',
                value: tokenValue,
                domain: '.stzy.com',
                path: '/',
                httpOnly: false,
                secure: true,
                sameSite: 'Lax'
            },
            {
                name: 'selectInfo',
                value: JSON.stringify(this.createSelectInfo()),
                domain: '.stzy.com',
                path: '/',
                httpOnly: false,
                secure: true,
                sameSite: 'Lax'
            }
        ]);

        // Setup context event handlers
        context.on('page', (page) => {
            this.setupPageHandlers(page);
        });

        this.contexts.set(contextId, context);

        this.logger?.debug('📄 浏览器上下文已创建并注入令牌', {
            contextId,
            tokenId: tokenData.id,
            tokenExists: !!tokenData.token
        });

        return { context, contextId };
    }

    /**
     * Set current parameter combination (following original pattern)
     * Integrated from original PlaywrightCrawler.js
     */
    setCurrentCombination(parameters) {
        // Ensure parameters have all required fields (both codes and names)
        this.currentCombination = {
            // Code fields (required for API requests and validation)
            studyPhaseCode: parameters.studyPhaseCode || '',
            subjectCode: parameters.subjectCode || '',
            textbookVersionCode: parameters.textbookVersionCode || '',
            ceciCode: parameters.ceciCode || '',
            catalogCode: parameters.catalogCode || '',

            // Name fields (for display and directory creation)
            studyPhaseName: parameters.studyPhaseName || '',
            subjectName: parameters.subjectName || '',
            textbookVersionName: parameters.textbookVersionName || '',
            ceciName: parameters.ceciName || '',
            catalogName: parameters.catalogName || ''
        };

        this.logger?.info(`📂 设置当前任务组合`, {
            combination: `${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}/${this.currentCombination.catalogName}`,
            codes: {
                studyPhaseCode: this.currentCombination.studyPhaseCode,
                subjectCode: this.currentCombination.subjectCode,
                textbookVersionCode: this.currentCombination.textbookVersionCode,
                ceciCode: this.currentCombination.ceciCode,
                catalogCode: this.currentCombination.catalogCode
            }
        });
    }

    /**
     * Create selectInfo object for cookie injection (following original pattern)
     */
    createSelectInfo() {
        if (!this.currentCombination) {
            return {
                dictCode: '',
                dictName: '',
                studyPhaseCode: '',
                studyPhaseName: '',
                childList: null,
                gray: false
            };
        }

        return {
            dictCode: this.currentCombination.subjectCode,
            dictName: this.currentCombination.subjectName,
            studyPhaseCode: this.currentCombination.studyPhaseCode,
            studyPhaseName: this.currentCombination.studyPhaseName,
            childList: null,
            gray: false
        };
    }

    /**
     * Setup page event handlers
     */
    setupPageHandlers(page) {
        page.on('console', (msg) => {
            if (msg.type() === 'error') {
                this.logger?.debug('🖥️ 页面控制台错误', { message: msg.text() });
            }
        });

        page.on('pageerror', (error) => {
            this.logger?.debug('🖥️ 页面错误', { error: error.message });
        });

        page.on('requestfailed', (request) => {
            this.logger?.debug('🌐 请求失败', {
                url: request.url(),
                failure: request.failure()?.errorText
            });
        });
    }

    /**
     * Create new page with enhanced configuration
     */
    async createPage(contextId = null) {
        let context;
        let actualContextId = contextId;

        if (contextId && this.contexts.has(contextId)) {
            context = this.contexts.get(contextId);
        } else {
            const result = await this.createContext();
            context = result.context;
            actualContextId = result.contextId;
        }

        const page = await context.newPage();
        const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        // Configure page settings
        await page.setDefaultTimeout(this.config.pageTimeout);
        await page.setDefaultNavigationTimeout(this.config.pageTimeout);

        this.pages.set(pageId, { page, contextId: actualContextId });

        this.logger?.debug('📃 页面已创建', { pageId, contextId: actualContextId });
        return { page, pageId, contextId: actualContextId };
    }

    /**
     * Create browser and page with token injection (following original PlaywrightCrawler pattern)
     * This method automatically gets a token and injects it into the browser instance
     */
    async createBrowserAndPageWithToken() {
        try {
            // Get available token from TokenManager (following original pattern)
            if (!this.tokenManager) {
                throw new Error('TokenManager不可用');
            }

            // Get available token from TokenLifecycleManager
            const availableTokenData = this.tokenManager.getAvailableToken();
            if (!availableTokenData) {
                throw new Error('未找到可用令牌');
            }

            const { tokenId, token } = availableTokenData;

            // Debug logging
            this.logger?.debug('🔍 已接收令牌数据', {
                tokenId,
                tokenExists: !!token,
                tokenKeys: token ? Object.keys(token) : 'null'
            });

            // Assign token to this instance
            const assigned = this.tokenManager.assignToken(tokenId);
            if (!assigned) {
                throw new Error(`分配令牌失败 ${tokenId}`);
            }

            this.logger?.info(`🔍 正在使用令牌[${tokenId}]创建浏览器并验证...`);

            // Clean up existing browser if any (before setting new token)
            await this.cleanup();

            // Store current token for this instance (use token.id since it already has the correct id)
            this.currentToken = token;

            // Launch new browser
            await this.launchBrowser();

            // Create context with token injection (following original pattern)
            const { context, contextId } = await this.createContextWithToken(token);

            // Create page from the context
            const page = await context.newPage();
            const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

            // Configure page settings
            await page.setDefaultTimeout(this.config.pageTimeout);
            await page.setDefaultNavigationTimeout(this.config.pageTimeout);

            // Store page info
            this.pages.set(pageId, { page, contextId });

            // Store the main page for this parameter processing
            this.mainPage = page;
            this.mainPageId = pageId;
            this.mainContextId = contextId;

            // 启动代理健康检查
            if (this.currentProxy) {
                this.startProxyHealthCheck();
            }

            this.logger?.info(`✅ 已成功创建浏览器和页面并注入令牌`, {
                tokenId: this.currentToken.id,
                pageId,
                contextId,
                proxyEnabled: !!this.currentProxy
            });

            return { page, pageId, contextId, token: this.currentToken };

        } catch (error) {
            const enhancedError = this.errorManager?.createEnhancedError(error, {
                operation: 'create_browser_with_token'
            }) || error;

            this.logger?.error('❌ 创建浏览器和令牌失败', { error: enhancedError.message });
            throw enhancedError;
        }
    }

    /**
     * Scrape data with comprehensive error handling and retry logic
     * Enhanced with detailed boundary checking and page validation from original crawler
     */
    async scrapeData(parameters, token = null) {
        const operationId = this.logger?.startOperation('scrape_data', { parameters });
        const startTime = Date.now();

        try {
            // Validate inputs
            if (!parameters) {
                throw new Error('爬取需要参数');
            }

            // Get or assign token
            const actualToken = token || await this.getAvailableToken();
            if (!actualToken) {
                throw new Error('没有可用的有效令牌进行爬取');
            }

            // Create page for scraping
            const { page, pageId, contextId } = await this.createPage();

            try {
                // Set authentication token
                await this.setAuthenticationToken(page, actualToken);

                // Enhanced scraping with detailed page processing
                const scrapingResult = await this.performEnhancedScraping(page, parameters, actualToken);

                const duration = Date.now() - startTime;
                this.updateMetrics(true, duration);

                // Record token usage
                if (this.tokenManager) {
                    this.tokenManager.recordTokenUsage(actualToken.tokenId, true, duration);
                }

                this.logger?.completeOperation('scrape_data', operationId, {
                    parameters,
                    duration,
                    savedPath: scrapingResult.savedPath,
                    dataSize: scrapingResult.totalCount || 0
                });

                return {
                    success: true,
                    data: scrapingResult.data,
                    savedPath: scrapingResult.savedPath,
                    duration: duration,
                    token: actualToken,
                    totalCount: scrapingResult.totalCount,
                    pagesProcessed: scrapingResult.pagesProcessed
                };

            } finally {
                // Cleanup page
                await this.closePage(pageId);
            }

        } catch (error) {
            const duration = Date.now() - startTime;
            this.updateMetrics(false, duration);

            const enhancedError = this.errorManager?.createEnhancedError(error, {
                operation: 'scrape_data',
                parameters: parameters,
                token: token?.tokenId
            }) || error;

            // Record token usage failure
            if (token && this.tokenManager) {
                this.tokenManager.recordTokenUsage(token.tokenId, false, duration);
            }

            this.logger?.failOperation('scrape_data', operationId, enhancedError, {
                parameters,
                duration
            });

            // Determine if error is retryable
            const recoveryStrategy = this.errorManager?.getRecoveryStrategy(enhancedError);
            enhancedError.retryable = recoveryStrategy?.retryable || false;

            throw enhancedError;
        }
    }

    /**
     * Process single parameter combination (following original PlaywrightCrawler pattern)
     * Each call processes one parameter combination with its own browser instance
     */
    async processSingleParameter(parameters) {
        const startTime = Date.now();
        this.logger?.info(`🎯 开始处理参数`, {
            parameters: {
                studyPhaseName: parameters.studyPhaseName,
                subjectName: parameters.subjectName,
                textbookVersionName: parameters.textbookVersionName,
                ceciName: parameters.ceciName,
                catalogName: parameters.catalogName
            }
        });

        try {
            // Set current combination (following original pattern)
            this.setCurrentCombination(parameters);

            // 早期完成检查：如果params文件中有maxPages信息且文件已完整，直接跳过
            const earlyCheck = this.earlyCompletionCheck(parameters);
            if (earlyCheck.shouldSkip) {
                this.logger?.info(`🎉 早期检测完成，跳过处理`, {
                    reason: earlyCheck.reason,
                    totalFiles: earlyCheck.totalFiles,
                    maxPages: earlyCheck.maxPages
                });

                return {
                    success: true,
                    totalCount: earlyCheck.totalFiles * 10, // 估算总数据量
                    pagesProcessed: earlyCheck.totalFiles,
                    savedPath: this.generateOutputPath(parameters).encodedPath,
                    skipped: true,
                    reason: earlyCheck.reason
                };
            }

            // Create browser instance with token injection (following original pattern)
            await this.createBrowserAndPageWithToken();
            this.logger?.info(`✅ 浏览器实例创建成功，开始页面爬取`);

            // Create output directory for this parameter combination
            const pathInfo = this.generateOutputPath(parameters);
            const outputPath = pathInfo.encodedPath;

            if (!fs.existsSync(outputPath)) {
                fs.mkdirSync(outputPath, { recursive: true });
            }

            this.logger?.info(`📂 输出目录已创建`, {
                encodedPath: pathInfo.encodedPath,
                originalPath: pathInfo.originalPath,
                parameters: {
                    studyPhaseName: parameters.studyPhaseName,
                    subjectName: parameters.subjectName,
                    textbookVersionName: parameters.textbookVersionName,
                    ceciName: parameters.ceciName,
                    catalogName: parameters.catalogName
                }
            });

            // Initialize data collection variables
            let allData = [];
            let totalCount = 0;
            let pagesProcessed = 0;

            // Get max pages (default value)
            let maxPages = this.config.maxPages || 50;
            let consecutiveEmptyPages = 0;
            const maxConsecutiveEmptyPages = 3;
            let isSettingsConfigured = false;

            // Crawl pages - force start from page 1 for debugging
            let currentPage = 1; // Force start from page 1 to ensure configuration happens

            while (currentPage <= maxPages) {
                try {
                    this.logger?.debug(`🚀 正在处理第${currentPage}/${maxPages}页`, { parameters, currentPage, maxPages });

                    // Check if page file already exists
                    const pageFilePath = path.join(outputPath, `${currentPage}.json`);
                    if (fs.existsSync(pageFilePath)) {
                        this.logger?.debug(`⏭️ 第${currentPage}页已存在，跳过`);
                        currentPage++;
                        continue;
                    }

                    // Use the main page for this parameter
                    const page = this.mainPage;
                    if (!page) {
                        throw new Error('主页面不可用');
                    }

                    // Navigate and configure on first page or if settings not configured
                    console.log(`🔧 检查配置条件 (currentPage=${currentPage}, isSettingsConfigured=${isSettingsConfigured})`);
                    if (currentPage === 1 || !isSettingsConfigured) {
                        try {
                            await this.navigateToPage(page, parameters);
                        } catch (error) {
                            // Check if it's a token validation error
                            if (error.message.includes('Token验证失败')) {
                                this.logger?.error(`🚫 页面导航时检测到token失效: ${error.message}`);

                                // 模拟用户发现token问题的行为
                                await this.humanBehavior.simulateTokenSwitch(page);

                                return {
                                    success: false,
                                    data: [],
                                    totalPages: 0,
                                    currentPage: 0,
                                    needSwitchToken: true,
                                    error: `导航时token验证失败: ${error.message}`
                                };
                            }
                            // Re-throw other errors
                            throw error;
                        }

                        await this.configurePageSettings(page);
                        isSettingsConfigured = true;

                        // Get actual max pages from pagination after first page setup
                        try {
                            const actualMaxPages = await this.getMaxPageFromPagination(page);
                            if (actualMaxPages > 0) {
                                maxPages = actualMaxPages;
                                this.logger?.info(`📄 已更新最大页数为: ${maxPages}`);

                                // 实时更新params文件中的最大页数
                                await this.updateParamsMaxPages(parameters, maxPages);

                                // 智能进度检测：检查现有文件并决定处理策略
                                const progressInfo = this.determineStartPage(outputPath, maxPages);

                                if (progressInfo.isComplete) {
                                    // 如果已完成，直接返回成功结果
                                    this.logger?.info(`🎉 Catalog已完成，跳过处理`, {
                                        totalFiles: progressInfo.existingFiles,
                                        maxPages: progressInfo.maxPages
                                    });

                                    return {
                                        success: true,
                                        totalCount: progressInfo.existingFiles * 10, // 估算总数据量
                                        pagesProcessed: progressInfo.existingFiles,
                                        savedPath: outputPath,
                                        skipped: true,
                                        reason: 'Already completed based on file count'
                                    };
                                } else if (progressInfo.startPage > 1) {
                                    // 如果有部分进度，更新起始页面
                                    currentPage = progressInfo.startPage;
                                    this.logger?.info(`🔄 基于现有进度，从第${currentPage}页开始处理`, {
                                        existingFiles: progressInfo.existingFiles,
                                        startPage: progressInfo.startPage,
                                        maxPages: progressInfo.maxPages
                                    });

                                    // 跳过第一页的处理，直接进入分页导航
                                    continue;
                                }
                            }
                        } catch (error) {
                            this.logger?.warn(`⚠️ 获取最大页数失败，使用默认值: ${error.message}`);
                        }
                    }

                // Navigate to specific page if not the first page
                if (currentPage > 1) {
                    // 模拟操作间的自然停顿
                    await this.humanBehavior.simulateNaturalPause();

                    const paginationResult = await this.handlePagination(page, currentPage, 3);
                    if (!paginationResult.success) {
                        this.enhancedLogger?.error(`❌ 导航到第${currentPage}页失败: ${paginationResult.reason}`);
                        break;
                    }

                    // 记录分页结果详情
                    if (paginationResult.forcedSuccess) {
                        this.enhancedLogger?.warn(`⚠️ 第${currentPage}页导航通过强制成功策略完成`, {
                            attempts: paginationResult.attempts,
                            originalError: paginationResult.originalError,
                            actualPage: paginationResult.actualPage
                        });
                    } else {
                        this.enhancedLogger?.info(`✅ 第${currentPage}页导航成功`, {
                            attempts: paginationResult.attempts,
                            actualPage: paginationResult.actualPage
                        });
                    }
                }

                // Check for loading state and handle timeouts
                const loadingCheck = await this.checkLoadingState(page);
                if (loadingCheck.needAction) {
                    const timeoutResult = await this.handleLoadingTimeout(page, `Page ${currentPage}`);
                    if (timeoutResult.needSwitchToken) {
                        throw new Error(`第${currentPage}页加载超时: ${timeoutResult.reason}`);
                    }
                }

                // Validate page elements
                const elementCheck = await this.checkPageElementsWithRetry(page, `Page ${currentPage}`);
                if (!elementCheck.valid) {
                    if (elementCheck.needSwitchToken) {
                        throw new Error(`第${currentPage}页验证失败: ${elementCheck.reason}`);
                    }
                }

                // Check for "no content found" message (boundary condition)
                const noContentCheck = await this.checkNoContentFound(page);
                if (noContentCheck.noContentFound) {
                    this.logger?.info(`✅ 在第${currentPage}页到达内容末尾`, {
                        reason: noContentCheck.reason,
                        message: noContentCheck.message
                    });
                    break;
                }

                // Wait for API response using continuous listening
                this.logger?.debug(`⏳ 等待第${currentPage}页API响应...`);
                const apiResponse = await this.waitForCapturedResponse(currentPage);

                // Check for timeout
                if (apiResponse.timeout) {
                    this.logger?.warn(`⏰ 第${currentPage}页响应超时`);
                    break;
                }

                // Check for token ban
                if (apiResponse.tokenBanned) {
                    this.enhancedLogger?.error(`🚫 在第${currentPage}页检测到403封禁，正在判断封禁类型...`);

                    // 先检测是IP封禁还是token封禁
                    const ipBanCheck = await this.checkIpBanStatus();

                    if (ipBanCheck.isIpBanned) {
                        // IP被封禁，发送通知并退出程序
                        this.enhancedLogger?.error(`🚫 确认为IP封禁: ${ipBanCheck.reason}`);

                        try {
                            const duration = this.processingStats?.startTime ?
                                `${((Date.now() - this.processingStats.startTime) / 1000 / 60).toFixed(2)} 分钟` :
                                'unknown';

                            // 发送IP封禁通知
                            if (this.feishuNotifier) {
                                await this.feishuNotifier.sendIpBannedNotification({
                                    instanceId: this.config?.instanceId || 'unknown',
                                    tokenFile: this.config?.tokensFile || 'unknown',
                                    currentParam: this.currentCombination ?
                                        `${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}/${this.currentCombination.catalogName}` :
                                        'unknown',
                                    duration: duration,
                                    ipAddress: 'current_ip'
                                });
                                this.enhancedLogger?.info('✅ IP封禁通知已发送');
                            }
                        } catch (notificationError) {
                            this.enhancedLogger?.error('❌ 发送IP封禁通知失败', {
                                error: notificationError.message
                            });
                        }

                        // IP封禁直接退出程序
                        throw new Error(`IP地址被封禁，程序退出: ${ipBanCheck.reason}`);
                    } else {
                        // 不是IP封禁，按原逻辑处理token问题
                        this.enhancedLogger?.info(`✅ IP未被封禁，处理token相关问题: ${ipBanCheck.reason}`);

                        // Handle token invalidation with automatic rotation
                        const rotationResult = await this.handleTokenInvalidation('API returned 403 error', true);

                        if (rotationResult.success) {
                            this.enhancedLogger?.info(`🔄 Token轮换成功，继续处理第${currentPage}页`);
                            // Continue with the new token - restart from current page
                            continue;
                        } else {
                            throw new Error(`令牌被禁用且轮换失败: ${rotationResult.error || rotationResult.reason}`);
                        }
                    }
                }

                // Process API response data
                if (apiResponse.data && apiResponse.data.data && apiResponse.data.data.list) {
                    const list = apiResponse.data.data.list;

                    if (list.length > 0) {
                        // Save page data with complete information like original crawler
                        await this.savePageData(currentPage, list, apiResponse.data, apiResponse.requestData, pathInfo, parameters);

                        allData = allData.concat(list);
                        totalCount += list.length;
                        pagesProcessed++;

                        this.logger?.debug(`✅ 第${currentPage}页处理完成`, {
                            itemCount: list.length,
                            totalCount
                        });
                    } else {
                        this.logger?.warn(`⚠️ 第${currentPage}页未找到数据`);
                        break;
                    }
                } else {
                    this.logger?.warn(`⚠️ 第${currentPage}页响应格式无效`);
                    break;
                }

                currentPage++;

                // Add intelligent delay between pages
                await this.intelligentDelay();

            } catch (error) {
                this.logger?.error(`❌ 处理第${currentPage}页时出错`, {
                    error: error.message,
                    parameters
                });
                throw error;
            }
            }

            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            this.logger?.info(`✅ 参数处理完成`, {
                parameters: {
                    studyPhaseName: parameters.studyPhaseName,
                    subjectName: parameters.subjectName,
                    textbookVersionName: parameters.textbookVersionName,
                    ceciName: parameters.ceciName,
                    catalogName: parameters.catalogName
                },
                totalCount,
                pagesProcessed,
                duration: `${duration.toFixed(2)} seconds`
            });

            return {
                data: allData,
                savedPath: outputPath,
                totalCount,
                pagesProcessed
            };

        } catch (error) {
            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;

            // Check if this is a TokenExhaustedException - if so, re-throw immediately
            if (error instanceof TokenExhaustedException || error.name === 'TokenExhaustedException') {
                this.logger?.error(`🚫 所有令牌已耗尽，无法继续处理参数`, {
                    parameters: {
                        studyPhaseName: parameters.studyPhaseName,
                        subjectName: parameters.subjectName,
                        textbookVersionName: parameters.textbookVersionName,
                        ceciName: parameters.ceciName,
                        catalogName: parameters.catalogName
                    },
                    availableTokens: error.availableTokens,
                    totalTokens: error.totalTokens,
                    duration: `${duration.toFixed(2)} seconds`
                });

                // Re-throw TokenExhaustedException to be handled by orchestrator
                throw error;
            }

            this.logger?.error(`❌ 参数处理失败`, {
                parameters: {
                    studyPhaseName: parameters.studyPhaseName,
                    subjectName: parameters.subjectName,
                    textbookVersionName: parameters.textbookVersionName,
                    ceciName: parameters.ceciName,
                    catalogName: parameters.catalogName
                },
                error: error.message,
                duration: `${duration.toFixed(2)} seconds`
            });

            throw error;

        } finally {
            // Clean up browser resources for this parameter (following original pattern)
            try {
                await this.cleanup();
                this.logger?.debug(`🧹 参数的浏览器资源已清理`);
            } catch (cleanupError) {
                this.logger?.warn(`⚠️ 清理浏览器资源失败: ${cleanupError.message}`);
            }
        }
    }

    /**
     * Get available token from token manager
     */
    async getAvailableToken() {
        if (!this.tokenManager) {
            throw new Error('令牌管理器未配置');
        }

        const tokenInfo = this.tokenManager.getAvailableToken();
        if (!tokenInfo) {
            // Check total tokens and available tokens for better error reporting
            const totalTokens = this.tokenManager.tokens ? this.tokenManager.tokens.size : 0;
            const validTokens = this.tokenManager.getValidTokenCount ? this.tokenManager.getValidTokenCount() : 0;

            this.logger?.error('🚫 所有令牌已耗尽或无效，正在检查IP封禁状态...', {
                totalTokens,
                validTokens
            });

            // 在抛出TokenExhaustedException之前，先检查是否是IP封禁导致的
            try {
                const ipBanCheck = await this.checkIpBanStatus();

                if (ipBanCheck.isIpBanned) {
                    // IP被封禁，发送IP封禁通知并退出
                    this.logger?.error(`🚫 确认为IP封禁: ${ipBanCheck.reason}`);

                    try {
                        if (this.feishuNotifier) {
                            await this.feishuNotifier.sendIpBannedNotification({
                                instanceId: this.config?.instanceId || 'unknown',
                                tokenFile: this.config?.tokensFile || 'unknown',
                                currentParam: this.currentCombination ?
                                    `${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}/${this.currentCombination.catalogName}` :
                                    'unknown',
                                duration: 'unknown',
                                ipAddress: 'current_ip'
                            });
                            this.logger?.info('✅ IP封禁通知已发送');
                        }
                    } catch (notificationError) {
                        this.logger?.error('❌ 发送IP封禁通知失败', {
                            error: notificationError.message
                        });
                    }

                    // IP封禁直接退出程序
                    throw new Error(`IP地址被封禁，程序退出: ${ipBanCheck.reason}`);
                } else {
                    // 不是IP封禁，是真正的token耗尽
                    this.logger?.info(`✅ IP未被封禁，确认为token耗尽: ${ipBanCheck.reason}`);
                }
            } catch (ipCheckError) {
                // IP检测失败，按原逻辑处理
                this.logger?.warn(`⚠️ IP封禁检测失败，按token耗尽处理: ${ipCheckError.message}`);
            }

            throw new TokenExhaustedException(
                `所有可用令牌已耗尽或无效 (总计: ${totalTokens}, 有效: ${validTokens})`,
                validTokens,
                totalTokens
            );
        }

        // Assign token to this instance
        this.tokenManager.assignToken(tokenInfo.tokenId);

        // Store current token for later reference
        this.currentToken = {
            tokenId: tokenInfo.tokenId,
            token: tokenInfo.token.token,
            phone: tokenInfo.token.phone
        };

        return this.currentToken;
    }

    /**
     * Get current token
     */
    getCurrentToken() {
        return this.currentToken;
    }

    /**
     * Handle token invalidation and automatic rotation
     */
    async handleTokenInvalidation(reason, shouldRetry = true) {
        this.enhancedLogger?.error(`🚫 Token失效处理`, {
            reason,
            shouldRetry,
            invalidatedTokenId: this.currentToken?.id
        });

        // Mark current token as permanently invalid
        if (this.currentToken && this.tokenManager) {
            this.tokenManager.markTokenPermanentlyInvalid(this.currentToken.id, reason);
        }

        // If retry is enabled, attempt to get a new token
        if (shouldRetry && this.tokenManager) {
            try {
                this.enhancedLogger?.info(`🔄 尝试获取新的Token进行轮换`);

                // Clean up current browser resources
                await this.cleanup();

                // Create new browser instance with new token
                await this.createBrowserAndPageWithToken();

                this.enhancedLogger?.info(`✅ Token轮换成功`, {
                    newTokenId: this.currentToken?.id
                });

                return {
                    success: true,
                    newTokenId: this.currentToken?.id,
                    action: 'token_rotated'
                };

            } catch (error) {
                this.enhancedLogger?.error(`❌ Token轮换失败`, {
                    error: error.message
                });

                return {
                    success: false,
                    error: error.message,
                    action: 'rotation_failed'
                };
            }
        }

        return {
            success: false,
            action: 'no_retry',
            reason: 'Token失效且未启用重试'
        };
    }

    /**
     * Set up early 403 detection listener
     */
    setupEarly403Detection(page) {
        let has403Error = false;
        let tokenValidationError = null;

        const earlyResponseListener = async (response) => {
            try {
                // Check for 403 status code
                if (response.status() === 403) {
                    has403Error = true;
                    tokenValidationError = `HTTP 403 status from ${response.url()}`;
                    this.logger?.warn(`🚫 检测到403状态码: ${response.url()}`);
                    return;
                }

                // Check for 403 in response body for API calls
                if (response.url().includes('api') || response.url().includes('stzy.com')) {
                    try {
                        const responseData = await response.json();
                        if (responseData && responseData.code === 403) {
                            has403Error = true;
                            tokenValidationError = `API response code 403 from ${response.url()}`;
                            this.logger?.warn(`🚫 检测到API响应403错误: ${response.url()}`);
                        }
                    } catch (jsonError) {
                        // Ignore JSON parsing errors for non-JSON responses
                    }
                }
            } catch (error) {
                // Ignore response processing errors
            }
        };

        return {
            listener: earlyResponseListener,
            hasError: () => has403Error,
            getError: () => tokenValidationError,
            cleanup: () => page.off('response', earlyResponseListener)
        };
    }

    /**
     * Set authentication token on page
     */
    async setAuthenticationToken(page, tokenInfo) {
        try {
            // Set up early 403 detection
            const detection = this.setupEarly403Detection(page);
            page.on('response', detection.listener);

            // Set token in localStorage or cookies as needed
            await page.addInitScript((token) => {
                localStorage.setItem('authToken', token);
                localStorage.setItem('userToken', token);
            }, tokenInfo.token);

            // Set authorization header for requests
            await page.setExtraHTTPHeaders({
                'Authorization': `Bearer ${tokenInfo.token}`,
                'X-Auth-Token': tokenInfo.token
            });

            // Wait a moment to see if any immediate 403 responses occur
            await this.sleep(1000);

            // Check for immediate 403 errors after setting token
            if (detection.hasError()) {
                detection.cleanup();
                throw new Error(`Token验证失败: ${detection.getError()}`);
            }

            // Keep the listener active for continued monitoring
            this.logger?.debug('🔐 认证令牌已设置', { tokenId: tokenInfo.tokenId });

        } catch (error) {
            throw new Error(`设置认证令牌失败: ${error.message}`);
        }
    }

    /**
     * Navigate to target page with retry logic and early token validation
     */
    async navigateToPage(page, parameters) {
        const baseURL = this.config.baseURL || 'https://zj.stzy.com/create-paper/chapter';
        const targetURL = this.buildTargetURL(baseURL, parameters);

        // Set up early 403 detection before navigation
        const detection = this.setupEarly403Detection(page);
        page.on('response', detection.listener);

        let lastError;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                this.logger?.debug('🧭 正在导航到页面', { url: targetURL, attempt });

                const response = await page.goto(targetURL, {
                    waitUntil: 'networkidle',
                    timeout: this.config.requestTimeout
                });

                if (!response.ok()) {
                    throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
                }

                // Wait for page to be ready
                await this.waitForPageReady(page);

                // Check for early 403 detection after page load
                if (detection.hasError()) {
                    detection.cleanup();
                    throw new Error(`Token验证失败: ${detection.getError()}`);
                }

                this.logger?.debug('✅ 导航成功', { url: targetURL });

                // Keep listener active for continued monitoring
                return response;

            } catch (error) {
                lastError = error;
                this.logger?.warn(`❌ 第${attempt}次导航尝试失败`, {
                    url: targetURL,
                    error: error.message
                });

                // If it's a token validation error, don't retry
                if (error.message.includes('Token验证失败')) {
                    detection.cleanup();
                    throw error;
                }

                if (attempt < this.config.maxRetries) {
                    const delay = this.config.retryDelay * attempt;
                    await this.sleep(delay);
                }
            }
        }

        // Remove listener before throwing final error
        detection.cleanup();
        throw new Error(`导航在${this.config.maxRetries}次尝试后失败: ${lastError.message}`);
    }

    /**
     * Build target URL from parameters
     */
    buildTargetURL(baseURL, parameters) {
        const queryParams = new URLSearchParams();
        
        if (parameters.studyPhaseCode) queryParams.set('studyPhaseCode', parameters.studyPhaseCode);
        if (parameters.subjectCode) queryParams.set('subjectCode', parameters.subjectCode);
        if (parameters.textbookVersionCode) queryParams.set('textbookVersionCode', parameters.textbookVersionCode);
        if (parameters.catalogCode) queryParams.set('catalogCode', parameters.catalogCode);
        if (parameters.chapterCode) queryParams.set('chapterCode', parameters.chapterCode);
        if (parameters.sectionCode) queryParams.set('sectionCode', parameters.sectionCode);

        return `${baseURL}?${queryParams.toString()}`;
    }

    /**
     * Wait for page to be ready for scraping
     */
    async waitForPageReady(page) {
        try {
            // Wait for basic page elements
            await page.waitForLoadState('networkidle', { timeout: 10000 });

            // Wait for specific elements that indicate page is ready
            await page.waitForSelector('body', { timeout: 5000 });

            // 模拟用户页面加载后的行为
            await this.humanBehavior.afterPageLoad(page);

        } catch (error) {
            this.logger?.warn('⏰ 页面就绪等待超时', { error: error.message });
            // Continue anyway, as some pages might not have expected elements
        }
    }

    /**
     * Extract data from page
     */
    async extractData(page, parameters) {
        try {
            this.logger?.debug('📊 正在从页面提取数据');

            // Wait for content to load
            await this.sleep(this.getRandomDelay());

            // Extract page content
            const pageData = await page.evaluate(() => {
                return {
                    title: document.title,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                    content: document.body.innerHTML,
                    textContent: document.body.textContent,
                    metadata: {
                        charset: document.characterSet,
                        readyState: document.readyState,
                        referrer: document.referrer
                    }
                };
            });

            // Add parameter information
            pageData.parameters = parameters;
            pageData.extractedAt = new Date().toISOString();

            this.logger?.debug('✅ 数据提取完成', {
                contentSize: pageData.content?.length || 0,
                textSize: pageData.textContent?.length || 0
            });

            return pageData;

        } catch (error) {
            throw new Error(`数据提取失败: ${error.message}`);
        }
    }

    /**
     * Save extracted results to file
     */
    async saveResults(data, parameters) {
        try {
            const filename = this.generateFilename(parameters);
            const filepath = path.join(this.config.outputDir, filename);

            // Ensure directory exists
            const dir = path.dirname(filepath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Save data as JSON
            fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger?.debug('💾 结果已保存', { filepath, size: data.content?.length || 0 });
            return filepath;

        } catch (error) {
            throw new Error(`保存结果失败: ${error.message}`);
        }
    }

    /**
     * Generate filename for results
     */
    generateFilename(parameters) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const paramKey = [
            parameters.studyPhaseCode,
            parameters.subjectCode,
            parameters.textbookVersionCode,
            parameters.catalogCode,
            parameters.chapterCode,
            parameters.sectionCode
        ].filter(Boolean).join('_');

        return `scrape_${paramKey}_${timestamp}.json`;
    }

    /**
     * Test proxy connectivity using Node.js (参考原始 PlaywrightCrawler 模式)
     */
    async testProxyWithNodeJS(proxyConfig) {
        return new Promise((resolve) => {
            try {
                const testUrl = 'https://www.baidu.com/';
                const url = new URL(testUrl);
                const isHttps = url.protocol === 'https:';
                const httpModule = isHttps ? https : http;

                const options = {
                    hostname: url.hostname,
                    port: url.port || (isHttps ? 443 : 80),
                    path: url.pathname + url.search,
                    method: 'GET',
                    timeout: 10000,
                    headers: {
                        'User-Agent': this.config.userAgent
                    }
                };

                // 添加代理配置
                if (proxyConfig && proxyConfig.server) {
                    const proxyUrl = new URL(proxyConfig.server);
                    options.hostname = proxyUrl.hostname;
                    options.port = proxyUrl.port;
                    options.path = testUrl;

                    // 添加代理认证信息（proxyConfig.username 和 proxyConfig.password 来自代理的账号密码）
                    if (proxyConfig.username && proxyConfig.password) {
                        const auth = Buffer.from(`${proxyConfig.username}:${proxyConfig.password}`).toString('base64');
                        options.headers['Proxy-Authorization'] = `Basic ${auth}`;
                        
                        this.logger?.debug('🔐 添加代理认证信息', {
                            username: proxyConfig.username,
                            server: proxyConfig.server
                        });
                    } else {
                        this.logger?.debug('📡 使用无认证代理', {
                            server: proxyConfig.server
                        });
                    }
                }

                const req = httpModule.request(options, (res) => {
                    if (res.statusCode >= 200 && res.statusCode < 400) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                });

                req.on('error', () => resolve(false));
                req.on('timeout', () => {
                    req.destroy();
                    resolve(false);
                });

                req.setTimeout(10000);
                req.end();

            } catch (error) {
                resolve(false);
            }
        });
    }

    /**
     * Start proxy health check
     */
    startProxyHealthCheck() {
        if (!this.currentProxy || this.proxyHealthCheckInterval) {
            return;
        }

        this.proxyHealthCheckInterval = setInterval(async () => {
            try {
                const isHealthy = await this.testProxyWithNodeJS(this.currentProxy);
                if (!isHealthy) {
                    this.logger?.warn('🚫 代理健康检查失败', {
                        server: this.currentProxy.server
                    });
                    // 可以在这里添加代理切换逻辑
                } else {
                    this.logger?.debug('✅ 代理健康检查通过', {
                        server: this.currentProxy.server
                    });
                }
            } catch (error) {
                this.logger?.warn('⚠️ 代理健康检查出错', {
                    error: error.message,
                    server: this.currentProxy.server
                });
            }
        }, this.config.proxy?.healthCheckInterval || 120000); // 默认2分钟检查一次
    }

    /**
     * Stop proxy health check
     */
    stopProxyHealthCheck() {
        if (this.proxyHealthCheckInterval) {
            clearInterval(this.proxyHealthCheckInterval);
            this.proxyHealthCheckInterval = null;
            this.logger?.debug('🛑 代理健康检查已停止');
        }
    }



    /**
     * Navigate to page and configure settings
     */
    async navigateAndConfigure(page, baseURL) {
        this.logger?.debug('🔗 正在导航到基础URL并配置设置');

        await page.goto(baseURL, {
            waitUntil: 'networkidle',
            timeout: this.config.pageTimeout || 100000
        });

        // Wait for page to stabilize
        await page.waitForTimeout(2000);

        // Configure page settings based on parameters
        await this.configurePageSettings(page);
    }

    /**
     * Configure page settings (following original clickSettingElements logic)
     * Integrated from original PlaywrightCrawler.js
     */
    async configurePageSettings(page) {
        try {
            this.logger?.info('🔧 配置教材版本和册次设置...');

            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(1000);

            // Check page elements first
            const elementCheckResult = await this.checkPageElementsWithRetry(page, 'settings', 2);
            if (!elementCheckResult.valid) {
                if (elementCheckResult.needSwitchToken) {
                    throw new Error(`页面元素检查失败，token已失效: ${elementCheckResult.reason}`);
                } else {
                    throw new Error(`页面元素检查失败，可能网络问题: ${elementCheckResult.reason}`);
                }
            }

            // 查找并触发下拉菜单
            const selectSubjectElement = page.locator('#textbook_tree');
            await selectSubjectElement.waitFor({ state: 'visible', timeout: 30000 });

            const triggerElement = selectSubjectElement.locator('.ant-dropdown-trigger');

            // 点击触发下拉菜单
            await triggerElement.click();
            await page.waitForTimeout(1000);

            // Check loading state after dropdown trigger
            const loadingCheck = await this.checkLoadingState(page);
            if (loadingCheck.needAction) {
                const timeoutResult = await this.handleLoadingTimeout(page, '下拉菜单操作后');
                if (timeoutResult.needSwitchToken) {
                    throw new Error(`下拉菜单加载超时，需要切换token: ${timeoutResult.reason}`);
                }
            }

            // 查找下拉菜单内容
            const dropdownContents = await page.locator('.ant-dropdown-content').all();
            if (dropdownContents.length === 0) {
                throw new Error('下拉菜单未出现');
            }

            // 选择教材版本和册次
            await this.selectTextbookAndCeci(page, triggerElement, dropdownContents);

            // 平滑移开鼠标，取消hover状态
            await this.moveMouseAwayFromDropdown(page);

            // 确认选择结果
            const isSelectionConfirmed = await this.confirmSelection(page, triggerElement);
            if (!isSelectionConfirmed) {
                throw new Error('教材版本和册次选择确认失败');
            }

            await page.waitForLoadState('networkidle');

            // 在点击catalog之前设置持续网络监听，以捕获所有API请求
            this.logger?.info('🔧 设置持续网络监听以捕获所有API请求');
            this.setupContinuousNetworkListening(page);

            // 点击目录 - 这将触发第一页的 question/textbookQuery 请求
            this.logger?.info('📂 即将点击目录，这将触发第1页的API请求');
            await this.clickCatalogElement(page);

            this.logger?.info('✅ 教材版本、册次和目录设置完成，第1页请求已发送');

        } catch (error) {
            this.logger?.error('❌ 设置配置失败', { error: error.message });
            throw error;
        }
    }

    /**
     * Select textbook version and ceci (following original pattern)
     * Integrated from original PlaywrightCrawler.js
     * 修改逻辑：在hover时保持鼠标稳定，避免hover元素消失
     */
    async selectTextbookAndCeci(page, triggerElement, dropdownContents) {
        let foundTextbookVersion = false;
        let foundCeci = false;

        // 首先获取触发器元素的位置，用于稳定hover
        const triggerBox = await triggerElement.boundingBox();
        if (!triggerBox) {
            throw new Error('无法获取下拉菜单触发器位置');
        }

        // 计算稳定的hover位置（触发器中心）
        const stableHoverX = triggerBox.x + triggerBox.width / 2;
        const stableHoverY = triggerBox.y + triggerBox.height / 2;

        for (let i = 0; i < dropdownContents.length; i++) {
            const dropdown = dropdownContents[i];

            // 移动到稳定的hover位置并保持
            await page.mouse.move(stableHoverX, stableHoverY);
            await page.waitForTimeout(this.humanBehavior.randomDelay(300, 500));

            this.enhancedLogger?.debug('🖱️ 鼠标已移动到稳定hover位置，开始查找选项');

            const options = await dropdown.locator('.flex_warp').all();

            for (const option of options) {
                // 确保鼠标仍在稳定位置
                await page.mouse.move(stableHoverX, stableHoverY);
                await page.waitForTimeout(100);

                const pointerOptions = await option.locator('.pointer').all();

                for (const pointerOption of pointerOptions) {
                    const pointerTextContent = await pointerOption.textContent();
                    if (!pointerTextContent) continue;

                    this.enhancedLogger?.debug(`🔍 检查选项: ${pointerTextContent.trim()}`);

                    // 查找教材版本
                    if (!foundTextbookVersion && pointerTextContent.trim() === this.currentCombination.textbookVersionName) {
                        this.enhancedLogger?.info(`📌 找到教材版本: ${pointerTextContent.trim()}`);

                        // 保持在稳定hover位置
                        await page.mouse.move(stableHoverX, stableHoverY);
                        await page.waitForTimeout(200);

                        // 直接点击选项，不移动鼠标
                        await pointerOption.click();
                        foundTextbookVersion = true;

                        this.enhancedLogger?.info(`✅ 教材版本选择完成: ${pointerTextContent.trim()}`);

                        // 点击后保持在稳定位置
                        await page.mouse.move(stableHoverX, stableHoverY);
                        await page.waitForTimeout(this.humanBehavior.randomDelay(300, 500));
                        break;
                    }

                    // 查找册次
                    if (!foundCeci && pointerTextContent.trim() === this.currentCombination.ceciName) {
                        this.enhancedLogger?.info(`📌 找到册次: ${pointerTextContent.trim()}`);

                        // 保持在稳定hover位置
                        await page.mouse.move(stableHoverX, stableHoverY);
                        await page.waitForTimeout(200);

                        // 直接点击选项，不移动鼠标
                        await pointerOption.click();
                        foundCeci = true;

                        this.enhancedLogger?.info(`✅ 册次选择完成: ${pointerTextContent.trim()}`);

                        // 点击后保持在稳定位置
                        await page.mouse.move(stableHoverX, stableHoverY);
                        await page.waitForTimeout(this.humanBehavior.randomDelay(300, 500));
                        break;
                    }
                }

                // 如果已找到两个选项，跳出循环
                if (foundTextbookVersion && foundCeci) {
                    break;
                }
            }

            if (foundTextbookVersion && foundCeci) {
                break;
            }
        }

        if (!foundTextbookVersion) {
            throw new Error(`未找到教材版本: ${this.currentCombination.textbookVersionName}`);
        }
        if (!foundCeci) {
            throw new Error(`未找到册次: ${this.currentCombination.ceciName}`);
        }

        this.enhancedLogger?.info('✅ 教材版本和册次选择完成');
    }

    /**
     * 平滑移开鼠标，避免突然移动导致页面异常
     */
    async moveMouseAwayFromDropdown(page) {
        try {
            // 获取页面尺寸
            const viewport = page.viewportSize();
            const targetX = viewport.width * 0.1; // 移动到页面左侧10%位置
            const targetY = viewport.height * 0.1; // 移动到页面顶部10%位置

            // 平滑移动鼠标到安全位置
            await page.mouse.move(targetX, targetY);
            await page.waitForTimeout(this.humanBehavior.randomDelay(300, 600));

            this.enhancedLogger?.debug('🖱️ 鼠标已移动到安全位置，下拉菜单hover状态已取消');
        } catch (error) {
            this.enhancedLogger?.warn('⚠️ 移动鼠标时出现问题，使用备用方案', { error: error.message });
            // 备用方案：移动到页面左上角
            await page.mouse.move(50, 50);
            await page.waitForTimeout(500);
        }
    }

    /**
     * Confirm selection results (following original pattern)
     * Integrated from original PlaywrightCrawler.js
     */
    async confirmSelection(page, triggerElement, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await page.waitForTimeout(1000);

                const titleElements = await triggerElement.locator('[title]').all();
                let foundTextbookVersion = false;
                let foundCeci = false;

                for (const element of titleElements) {
                    const titleValue = await element.getAttribute('title');
                    if (titleValue) {
                        if (titleValue.includes(this.currentCombination.textbookVersionName)) {
                            foundTextbookVersion = true;
                        }
                        if (titleValue.includes(this.currentCombination.ceciName)) {
                            foundCeci = true;
                        }
                    }
                }

                if (foundTextbookVersion && foundCeci) {
                    return true;
                } else {
                    if (attempt < maxRetries) {
                        await page.waitForTimeout(2000);
                        this.logger?.warn(`⚠️ 第${attempt}次确认选择失败，重试中...`);
                    }
                }
            } catch (error) {
                this.logger?.warn(`⚠️ 第${attempt}次确认选择时出错: ${error.message}`);
                if (attempt < maxRetries) {
                    await page.waitForTimeout(2000);
                }
            }
        }
        return false;
    }

    /**
     * Click catalog element (following original pattern)
     * Integrated from original PlaywrightCrawler.js
     */
    async clickCatalogElement(page) {
        try {
            this.logger?.info(`📂 选择目录: ${this.currentCombination.catalogName} (将触发第1页API请求)`);

            await page.waitForLoadState('networkidle');

            // 使用人类行为模拟的等待
            await page.waitForTimeout(this.humanBehavior.randomDelay(1500, 2500));

            // 等待教材树容器加载
            const textbookTree = page.locator('#textbook_tree');
            await textbookTree.waitFor({ state: 'visible', timeout: 30000 });

            // 查找tree组件
            const treeWrapper = textbookTree.locator('.tree-component-wrapper');
            await treeWrapper.waitFor({ state: 'visible', timeout: 30000 });

            const treeUl = treeWrapper.locator('ul[role="tree"]');
            await treeUl.waitFor({ state: 'visible', timeout: 30000 });

            // 查找目标目录元素
            const titleElements = await treeUl.locator('[title]').all();

            let targetElement = null;
            for (const element of titleElements) {
                const titleValue = await element.getAttribute('title');
                if (titleValue && titleValue.trim() === this.currentCombination.catalogName) {
                    targetElement = element;
                    break;
                }
            }

            if (!targetElement) {
                throw new Error(`未找到目录: ${this.currentCombination.catalogName}`);
            }

            // 使用人类行为模拟点击目录元素
            await this.humanBehavior.humanClick(page, targetElement, {
                description: `目录: ${this.currentCombination.catalogName}`
            });

            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(this.humanBehavior.randomDelay(1500, 2500));

            this.logger?.info(`✅ 目录选择完成: ${this.currentCombination.catalogName}，第1页API请求已发送`);

        } catch (error) {
            this.logger?.error('❌ 目录选择失败', { error: error.message });
            throw error;
        }
    }

    /**
     * Navigate to specific page number
     */
    async navigateToPageNumber(page, pageNumber) {
        this.logger?.debug(`📄 正在导航到第${pageNumber}页`);

        try {
            // Look for page input field and navigate
            const pageInput = page.locator('input.pagination_jump_page_input');
            if (await pageInput.count() > 0) {
                await pageInput.clear();
                await pageInput.fill(pageNumber.toString());
                await page.keyboard.press('Enter');
                await page.waitForTimeout(2000);
            }
        } catch (error) {
            this.logger?.warn(`导航到第${pageNumber}页失败`, { error: error.message });
        }
    }

    /**
     * Extract data from current page
     */
    async extractPageData(page, pageNumber) {
        try {
            this.logger?.debug(`📊 正在从第${pageNumber}页提取数据`);

            // Wait for content to load
            await page.waitForTimeout(2000);

            // Extract data based on page structure
            // This is a placeholder - implement based on actual page structure
            const data = await page.evaluate(() => {
                // Add actual data extraction logic here
                return [];
            });

            return data;

        } catch (error) {
            this.logger?.error(`从第${pageNumber}页提取数据失败`, { error: error.message });
            return [];
        }
    }

    /**
     * Intelligent delay with randomization
     */
    async intelligentDelay() {
        const delay = this.getRandomDelay();
        this.logger?.debug(`⏱️ 应用智能延迟: ${delay}ms`);
        await this.sleep(delay);
    }

    /**
     * Check for "no content found" message (boundary condition)
     * Integrated from original PlaywrightCrawler.js
     */
    async checkNoContentFound(page) {
        try {
            this.logger?.debug('🔍 正在检查"未找到内容"消息');

            // Wait for network requests to complete
            await page.waitForLoadState('networkidle', { timeout: 30000 });
            await page.waitForTimeout(2000);

            // Look for img elements with class="w120 h120"
            const imgElements = await page.locator('img.w120.h120').all();

            if (imgElements.length === 0) {
                return { noContentFound: false, reason: '未找到目标图片元素' };
            }

            this.logger?.debug(`🖼️ 找到${imgElements.length}个目标图片元素`);

            // Check each img element's siblings for the "no content" message
            for (let i = 0; i < imgElements.length; i++) {
                const img = imgElements[i];

                try {
                    const parent = img.locator('..');
                    const siblingPs = await parent.locator('p').all();

                    for (const p of siblingPs) {
                        const textContent = await p.textContent();
                        if (textContent && textContent.includes('没有查询到您想要的内容')) {
                            const isVisible = await p.isVisible();

                            this.logger?.debug(`🎯 找到"无内容"消息`, {
                                visible: isVisible,
                                text: textContent.trim()
                            });

                            if (isVisible) {
                                return {
                                    noContentFound: true,
                                    reason: '显示无内容消息',
                                    message: textContent.trim(),
                                    isVisible: isVisible
                                };
                            }
                        }
                    }
                } catch (error) {
                    this.logger?.warn(`检查第${i + 1}个图片元素时出错`, { error: error.message });
                }
            }

            return { noContentFound: false, reason: '未找到"无内容"消息' };

        } catch (error) {
            this.logger?.warn('检查无内容消息时出错', { error: error.message });
            return { noContentFound: false, reason: `Check error: ${error.message}` };
        }
    }

    /**
     * Check page elements with retry logic
     * Integrated from original PlaywrightCrawler.js
     */
    async checkPageElementsWithRetry(page, pageContext, maxRetries = 3) {
        let lastResult = null;

        this.logger?.debug(`🔍 正在检查页面元素 (${pageContext})`);

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            this.logger?.debug(`📍 第${attempt}/${maxRetries}次元素检查尝试`);

            const result = await this.checkPageElements(page);
            lastResult = result;

            if (result.valid) {
                if (attempt > 1) {
                    this.logger?.info(`✅ 第${attempt}次尝试页面元素有效`);
                } else {
                    this.logger?.debug(`✅ 页面元素检查通过`);
                }
                return result;
            }

            this.logger?.warn(`❌ 第${attempt}次元素检查失败: ${result.reason}`);

            // Check for critical failures that require token switch
            if (result.isLoginRedirect || result.isTokenError) {
                this.enhancedLogger?.error(`🚫 检测到关键故障，需要切换令牌`);

                const reason = result.isLoginRedirect ? '检测到登录重定向' : '检测到令牌错误';

                // Handle token invalidation with automatic rotation
                const rotationResult = await this.handleTokenInvalidation(reason, true);

                if (rotationResult.success) {
                    this.enhancedLogger?.info(`🔄 Token轮换成功，重新检查页面元素`);
                    // Return success to retry with new token
                    return { valid: true, reason: 'Token轮换后重试', tokenRotated: true };
                } else {
                    return { ...result, needSwitchToken: true, rotationFailed: true };
                }
            }

            // If possible network issue and not last attempt, try recovery
            if (result.possibleNetworkIssue && attempt < maxRetries) {
                this.logger?.debug(`🔄 正在尝试恢复网络问题...`);

                try {
                    await page.reload({
                        waitUntil: 'networkidle',
                        timeout: this.config.pageTimeout || 100000
                    });
                    await page.waitForTimeout(2000);
                } catch (refreshError) {
                    this.logger?.warn(`页面刷新失败`, { error: refreshError.message });
                    if (attempt === maxRetries) {
                        return { ...result, needSwitchToken: true };
                    }
                }
            } else if (attempt < maxRetries) {
                await page.waitForTimeout(3000);
            }
        }

        this.logger?.error(`❌ 所有${maxRetries}次元素检查尝试都失败了`);
        return lastResult || {
            valid: false,
            reason: `元素检查在${maxRetries}次尝试后失败 (${pageContext})`,
            needSwitchToken: true
        };
    }

    /**
     * Check page elements for validity
     * Integrated from original PlaywrightCrawler.js
     */
    async checkPageElements(page) {
        try {
            // Check if redirected to login page
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                return { valid: false, reason: `重定向到登录页面: ${currentUrl}`, isLoginRedirect: true };
            }

            // Check for key elements
            const elementsToCheck = [
                { selector: '#textbook_tree', name: '教材树容器' },
                { selector: '.ant-dropdown-trigger', name: '下拉触发器' },
                { selector: 'ul.ant-pagination', name: '分页组件' },
                { selector: 'input.pagination_jump_page_input', name: '页面跳转输入框' }
            ];

            const missingElements = [];
            const existingElements = [];

            for (const element of elementsToCheck) {
                try {
                    const count = await page.locator(element.selector).count();
                    if (count > 0) {
                        existingElements.push(element.name);
                    } else {
                        missingElements.push(element.name);
                    }
                } catch (error) {
                    missingElements.push(element.name);
                }
            }

            this.logger?.debug(`✅ 找到的元素: ${existingElements.join(', ')}`);
            if (missingElements.length > 0) {
                this.logger?.debug(`❌ 缺失的元素: ${missingElements.join(', ')}`);
            }

            // If most key elements are missing, likely a token/network issue
            if (missingElements.length >= 3) {
                return {
                    valid: false,
                    reason: `缺少关键元素: ${missingElements.join(', ')}`,
                    missingElements: missingElements,
                    existingElements: existingElements,
                    possibleNetworkIssue: true
                };
            }

            // Check for error elements
            const errorSelectors = [
                '.ant-result-error',
                '.error-page',
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const count = await page.locator(selector).count();
                    if (count > 0) {
                        return {
                            valid: false,
                            reason: `检测到错误元素: ${selector}`,
                            errorElement: selector,
                            isTokenError: true
                        };
                    }
                } catch (error) {
                    // Ignore check errors
                }
            }

            return { valid: true, reason: '页面元素检查通过' };

        } catch (error) {
            return {
                valid: false,
                reason: `元素检查异常: ${error.message}`,
                error: error.message,
                possibleNetworkIssue: true
            };
        }
    }

    /**
     * Check if current page is a login page
     */
    isLoginPage(url) {
        return url.includes('login') ||
               url.includes('auth') ||
               /https:\/\/www\.stzy\.com\/login\//.test(url);
    }

    /**
     * Get random delay between min and max
     */
    getRandomDelay() {
        return Math.floor(Math.random() * (this.config.maxDelay - this.config.minDelay + 1)) + this.config.minDelay;
    }

    /**
     * Sleep for specified milliseconds
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Update performance metrics
     */
    updateMetrics(success, duration) {
        this.metrics.totalRequests++;
        
        if (success) {
            this.metrics.successfulRequests++;
            this.successCount++;
        } else {
            this.metrics.failedRequests++;
            this.errorCount++;
        }

        this.requestCount++;

        // Track response times
        this.metrics.responseTimes.push(duration);
        if (this.metrics.responseTimes.length > 100) {
            this.metrics.responseTimes = this.metrics.responseTimes.slice(-100);
        }

        // Calculate average response time
        if (this.metrics.responseTimes.length > 0) {
            this.metrics.averageResponseTime = this.metrics.responseTimes.reduce((a, b) => a + b, 0) / this.metrics.responseTimes.length;
        }
    }

    /**
     * Close specific page
     */
    async closePage(pageId) {
        try {
            const pageInfo = this.pages.get(pageId);
            if (pageInfo) {
                await pageInfo.page.close();
                this.pages.delete(pageId);
                this.logger?.debug('📃 页面已关闭', { pageId });
            }
        } catch (error) {
            this.logger?.warn('❌ 关闭页面失败', { pageId, error: error.message });
        }
    }

    /**
     * Close specific context
     */
    async closeContext(contextId) {
        try {
            const context = this.contexts.get(contextId);
            if (context) {
                await context.close();
                this.contexts.delete(contextId);
                this.logger?.debug('📄 上下文已关闭', { contextId });
            }
        } catch (error) {
            this.logger?.warn('❌ 关闭上下文失败', { contextId, error: error.message });
        }
    }

    /**
     * Get crawler statistics
     */
    getStatistics() {
        return {
            ...this.metrics,
            isInitialized: this.isInitialized,
            isShuttingDown: this.isShuttingDown,
            activePages: this.pages.size,
            activeContexts: this.contexts.size,
            successRate: this.requestCount > 0 ? (this.successCount / this.requestCount) * 100 : 0,
            errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0
        };
    }

    /**
     * Cleanup browser resources (following original PlaywrightCrawler pattern)
     */
    async cleanup() {
        try {
            // Stop proxy health check
            this.stopProxyHealthCheck();

            // Clear main page reference
            this.mainPage = null;
            this.mainPageId = null;
            this.mainContextId = null;

            // Close all pages
            for (const pageId of this.pages.keys()) {
                await this.closePage(pageId);
            }

            // Close all contexts
            for (const contextId of this.contexts.keys()) {
                await this.closeContext(contextId);
            }

            // Close browser if it exists
            if (this.browser) {
                try {
                    await this.browser.close();
                } catch (browserError) {
                    this.logger?.warn(`⚠️ 关闭浏览器时出错: ${browserError.message}`);
                }
                this.browser = null;
            }

            // Release current token if assigned
            if (this.currentToken && this.tokenManager) {
                try {
                    this.tokenManager.releaseToken(this.currentToken.id);
                    this.logger?.debug(`🔓 已释放令牌 ${this.currentToken.id}`);
                } catch (tokenError) {
                    this.logger?.warn(`⚠️ 释放令牌时出错: ${tokenError.message}`);
                }
            }

            // Clear current token reference
            this.currentToken = null;

            this.logger?.debug('🧹 浏览器资源清理成功');

        } catch (cleanupError) {
            this.logger?.warn(`⚠️ 浏览器清理过程中出错: ${cleanupError.message}`);
            // Force clear all references
            this.mainPage = null;
            this.mainPageId = null;
            this.mainContextId = null;
            this.browser = null;

            // Force release token if assigned
            if (this.currentToken && this.tokenManager) {
                try {
                    this.tokenManager.releaseToken(this.currentToken.id);
                } catch (tokenError) {
                    // Ignore errors during force cleanup
                }
            }
            this.currentToken = null;
        }
    }

    /**
     * Clear browser cache and session data comprehensively
     * Used during token rotation to ensure clean state
     */
    async clearBrowserCache(context = null, page = null) {
        if (!this.config.browserCache.clearOnTokenRotation) {
            this.logger?.debug('🧹 Browser cache clearing disabled');
            return;
        }

        try {
            this.logger?.info('🧹 Starting comprehensive browser cache clearing');

            // Use provided context/page or current ones
            const targetContext = context || (this.mainContextId ? this.contexts.get(this.mainContextId) : null);
            const targetPage = page || this.mainPage;

            if (targetPage && this.config.browserCache.clearLocalStorage) {
                try {
                    // Clear localStorage
                    await targetPage.evaluate(() => {
                        if (typeof localStorage !== 'undefined') {
                            localStorage.clear();
                        }
                    });
                    this.logger?.debug('🧹 localStorage cleared');
                } catch (error) {
                    this.logger?.warn(`⚠️ Failed to clear localStorage: ${error.message}`);
                }
            }

            if (targetPage && this.config.browserCache.clearSessionStorage) {
                try {
                    // Clear sessionStorage
                    await targetPage.evaluate(() => {
                        if (typeof sessionStorage !== 'undefined') {
                            sessionStorage.clear();
                        }
                    });
                    this.logger?.debug('🧹 sessionStorage cleared');
                } catch (error) {
                    this.logger?.warn(`⚠️ Failed to clear sessionStorage: ${error.message}`);
                }
            }

            if (targetPage && this.config.browserCache.clearIndexedDB) {
                try {
                    // Clear IndexedDB
                    await targetPage.evaluate(() => {
                        if (typeof indexedDB !== 'undefined') {
                            return new Promise((resolve) => {
                                indexedDB.databases().then(databases => {
                                    Promise.all(databases.map(db => {
                                        return new Promise((dbResolve) => {
                                            const deleteReq = indexedDB.deleteDatabase(db.name);
                                            deleteReq.onsuccess = () => dbResolve();
                                            deleteReq.onerror = () => dbResolve();
                                        });
                                    })).then(() => resolve());
                                }).catch(() => resolve());
                            });
                        }
                    });
                    this.logger?.debug('🧹 IndexedDB cleared');
                } catch (error) {
                    this.logger?.warn(`⚠️ Failed to clear IndexedDB: ${error.message}`);
                }
            }

            if (targetContext) {
                if (this.config.browserCache.clearCookies) {
                    try {
                        // Clear all cookies
                        await targetContext.clearCookies();
                        this.logger?.debug('🧹 Cookies cleared');
                    } catch (error) {
                        this.logger?.warn(`⚠️ Failed to clear cookies: ${error.message}`);
                    }
                }

                if (this.config.browserCache.clearCache) {
                    try {
                        // Clear browser cache (requires CDP)
                        const cdpSession = await targetContext.newCDPSession(targetPage || await targetContext.newPage());
                        await cdpSession.send('Network.clearBrowserCache');
                        await cdpSession.detach();
                        this.logger?.debug('🧹 Browser cache cleared');
                    } catch (error) {
                        this.logger?.warn(`⚠️ Failed to clear browser cache: ${error.message}`);
                    }
                }
            }

            this.logger?.info('✅ Browser cache clearing completed');

        } catch (error) {
            this.logger?.error('❌ Failed to clear browser cache', { error: error.message });
            throw error;
        }
    }

    /**
     * Handle token rotation with comprehensive cache clearing and token re-injection
     */
    async handleTokenRotation(newTokenData) {
        try {
            this.logger?.info('🔄 Starting token rotation process', {
                oldTokenId: this.currentToken?.id,
                newTokenId: newTokenData?.id
            });

            // Step 1: Clear browser cache and session data
            if (this.config.browserCache.clearOnTokenRotation) {
                await this.clearBrowserCache();
            }

            // Step 2: Clean up current browser resources
            await this.cleanup();

            // Step 3: Create new browser instance with new token
            const result = await this.createBrowserAndPageWithToken();

            // Step 4: Validate new token if required
            if (this.config.browserCache.validateTokenAfterRotation) {
                await this.validateTokenAfterRotation();
            }

            this.logger?.info('✅ Token rotation completed successfully', {
                newTokenId: this.currentToken?.id,
                pageReady: !!this.mainPage
            });

            return {
                success: true,
                newTokenId: this.currentToken?.id,
                action: 'token_rotated_with_cache_clear'
            };

        } catch (error) {
            this.logger?.error('❌ Token rotation failed', { error: error.message });

            return {
                success: false,
                error: error.message,
                action: 'rotation_failed'
            };
        }
    }

    /**
     * Validate token after rotation by making a test request
     */
    async validateTokenAfterRotation() {
        if (!this.mainPage) {
            throw new Error('No main page available for token validation');
        }

        try {
            this.logger?.debug('🔍 Validating token after rotation');

            // Navigate to a test page to validate token
            const testUrl = this.config.baseURL || 'https://zj.stzy.com/create-paper/chapter';
            const response = await this.mainPage.goto(testUrl, {
                waitUntil: 'networkidle',
                timeout: this.config.requestTimeout
            });

            if (!response.ok()) {
                throw new Error(`Token validation failed: HTTP ${response.status()}`);
            }

            // Wait for page to be ready
            await this.waitForPageReady(this.mainPage);

            this.logger?.debug('✅ Token validation successful');

        } catch (error) {
            this.logger?.error('❌ Token validation failed', { error: error.message });
            throw new Error(`Token validation failed: ${error.message}`);
        }
    }

    /**
     * Shutdown crawler and cleanup resources
     */
    async shutdown() {
        if (this.isShuttingDown) return;
        this.isShuttingDown = true;

        this.logger?.info('🔄 正在关闭增强版Playwright爬虫');

        try {
            // Use cleanup method for consistency
            await this.cleanup();

            this.isInitialized = false;
            this.logger?.info('✅ 增强版Playwright爬虫关闭完成');

        } catch (error) {
            this.logger?.error('❌ 爬虫关闭过程中出错', { error: error.message });
        }
    }

    /**
     * Check loading state of the page
     * Integrated from original PlaywrightCrawler.js
     */
    async checkLoadingState(page, timeoutMs = 10000) {
        try {
            this.logger?.debug('🔍 正在检查页面加载状态');

            const startTime = Date.now();
            let isLoading = false;

            // Check for loading images
            const checkLoading = async () => {
                try {
                    const images = await page.locator('img.w80.h30').all();

                    for (const img of images) {
                        const src = await img.getAttribute('src');
                        const isVisible = await img.isVisible();

                        if (src === 'https://zj.stzy.com/images/create-paper/loading.gif' && isVisible) {
                            return { isLoading: true, element: img };
                        }
                    }

                    return { isLoading: false, element: null };
                } catch (error) {
                    return { isLoading: false, element: null };
                }
            };

            const initialCheck = await checkLoading();
            if (!initialCheck.isLoading) {
                return {
                    isLoading: false,
                    reason: '未检测到加载状态',
                    needAction: false
                };
            }

            this.logger?.debug('⏳ 检测到加载中，正在监控...');
            isLoading = true;

            // Monitor loading state
            while (isLoading && (Date.now() - startTime) < timeoutMs) {
                await page.waitForTimeout(1000);

                const currentCheck = await checkLoading();
                if (!currentCheck.isLoading) {
                    const duration = Date.now() - startTime;
                    this.logger?.debug(`✅ 加载在${duration}ms内完成`);
                    return {
                        isLoading: false,
                        reason: '加载完成',
                        duration: duration,
                        needAction: false
                    };
                }
            }

            if (isLoading) {
                const duration = Date.now() - startTime;
                this.logger?.warn(`⚠️ 加载在${duration}ms后超时`);

                return {
                    isLoading: true,
                    reason: '加载超时',
                    duration: duration,
                    needAction: true
                };
            }

            return {
                isLoading: false,
                reason: '检查完成',
                needAction: false
            };

        } catch (error) {
            this.logger?.warn('检查加载状态时出错', { error: error.message });
            return {
                isLoading: false,
                reason: `Check error: ${error.message}`,
                needAction: false
            };
        }
    }

    /**
     * Handle loading timeout situations
     * Integrated from original PlaywrightCrawler.js
     */
    async handleLoadingTimeout(page, pageContext = '') {
        try {
            this.logger?.warn(`🔄 正在处理${pageContext}的加载超时`);

            // Check if it's a token issue
            const tokenCheck = await this.checkTokenStatus(page);

            if (tokenCheck.isTokenBanned) {
                this.enhancedLogger?.error(`🚫 检测到令牌问题: ${tokenCheck.reason}`);

                // Handle token invalidation with automatic rotation
                const rotationResult = await this.handleTokenInvalidation(tokenCheck.reason, true);

                if (rotationResult.success) {
                    this.enhancedLogger?.info(`🔄 Token轮换成功，重新处理加载超时`);
                    return {
                        action: 'tokenRotated',
                        reason: 'Token轮换成功',
                        needReconfigure: true
                    };
                } else {
                    return {
                        action: 'switchToken',
                        reason: `Token轮换失败: ${rotationResult.error || rotationResult.reason}`,
                        needSwitchToken: true
                    };
                }
            }

            // Try refreshing the page
            this.logger?.debug('🔄 正在尝试刷新页面...');

            try {
                await page.reload({
                    waitUntil: 'networkidle',
                    timeout: this.config.pageTimeout || 100000
                });

                await page.waitForTimeout(3000);

                // Check loading state again
                const loadingCheck = await this.checkLoadingState(page, 5000);

                if (loadingCheck.needAction) {
                    this.logger?.warn('⚠️ 刷新后仍在加载，可能是令牌问题');
                    return {
                        action: 'switchToken',
                        reason: '刷新后仍在加载',
                        needSwitchToken: true
                    };
                }

                this.logger?.info('✅ 页面刷新成功');
                return {
                    action: 'reconfigure',
                    reason: '页面刷新成功',
                    needReconfigure: true
                };

            } catch (refreshError) {
                this.logger?.error('❌ 页面刷新失败', { error: refreshError.message });
                return {
                    action: 'switchToken',
                    reason: `页面刷新失败: ${refreshError.message}`,
                    needSwitchToken: true
                };
            }

        } catch (error) {
            this.logger?.error('❌ 处理加载超时时出错', { error: error.message });
            return {
                action: 'switchToken',
                reason: `超时处理失败: ${error.message}`,
                needSwitchToken: true
            };
        }
    }

    /**
     * Check token status
     * Integrated from original PlaywrightCrawler.js
     */
    async checkTokenStatus(page) {
        try {
            // Check current URL for login redirect
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                return {
                    isTokenBanned: true,
                    reason: `页面重定向到登录页面: ${currentUrl}`
                };
            }

            // Check for error elements
            const errorSelectors = [
                '.ant-result-error',
                '.error-page',
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]',
                '[class*="expired"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const errorElement = page.locator(selector);
                    const count = await errorElement.count();
                    if (count > 0) {
                        const isVisible = await errorElement.first().isVisible();
                        if (isVisible) {
                            return {
                                isTokenBanned: true,
                                reason: `错误元素可见: ${selector}`
                            };
                        }
                    }
                } catch (error) {
                    // Ignore individual selector errors
                }
            }

            // Check for missing key elements
            const keyElements = [
                '#textbook_tree',
                '.ant-dropdown-trigger',
                'ul.ant-pagination'
            ];

            let missingCount = 0;
            for (const selector of keyElements) {
                try {
                    const count = await page.locator(selector).count();
                    if (count === 0) {
                        missingCount++;
                    }
                } catch (error) {
                    missingCount++;
                }
            }

            // If most key elements are missing, likely token issue
            if (missingCount >= 2) {
                return {
                    isTokenBanned: true,
                    reason: `关键元素缺失 (${missingCount}/${keyElements.length})`
                };
            }

            return {
                isTokenBanned: false,
                reason: '令牌状态正常'
            };

        } catch (error) {
            return {
                isTokenBanned: false,
                reason: `令牌状态检查错误: ${error.message}`
            };
        }
    }

    /**
     * Generate output path for parameter combination
     * Integrated from original PlaywrightCrawler.js logic
     */
    generateOutputPath(parameters) {
        const pathComponents = [
            { original: parameters.studyPhaseName, encoded: this.encodeDirName(parameters.studyPhaseName) },
            { original: parameters.subjectName, encoded: this.encodeDirName(parameters.subjectName) },
            { original: parameters.textbookVersionName, encoded: this.encodeDirName(parameters.textbookVersionName) },
            { original: parameters.ceciName, encoded: this.encodeDirName(parameters.ceciName) },
            { original: parameters.catalogName, encoded: this.encodeDirName(parameters.catalogName) }
        ];

        const encodedPath = path.join(this.config.outputDir || './output', ...pathComponents.map(c => c.encoded));

        return {
            encodedPath: encodedPath,
            pathComponents: pathComponents,
            originalPath: pathComponents.map(c => c.original).join('/') // 用于显示和调试
        };
    }

    /**
     * Encode directory name for safe file system usage
     * Integrated from original PlaywrightCrawler.js
     */
    encodeDirName(name) {
        if (!name) return 'unknown';

        // 将中文和特殊字符进行URL编码
        const encoded = encodeURIComponent(name)
            .replace(/\./g, '%2E')  // 编码点号
            .replace(/!/g, '%21')   // 编码感叹号
            .replace(/'/g, '%27')   // 编码单引号
            .replace(/\(/g, '%28')  // 编码左括号
            .replace(/\)/g, '%29')  // 编码右括号
            .replace(/\*/g, '%2A'); // 编码星号

        return encoded;
    }

    /**
     * Decode directory name from encoded format
     * Integrated from original PlaywrightCrawler.js
     */
    decodeDirName(encodedName) {
        if (!encodedName) return 'unknown';

        try {
            const decoded = decodeURIComponent(encodedName);
            return decoded.trim();
        } catch (error) {
            console.warn(`⚠️ 解码目录名失败: ${encodedName}, 错误: ${error.message}`);
            return encodedName;
        }
    }



    /**
     * Navigate to specific page number
     */
    async navigateToPageNumber(page, pageNumber) {
        this.logger?.debug(`📄 正在导航到第${pageNumber}页`);

        try {
            // Look for page input field and navigate
            const pageInput = page.locator('input.pagination_jump_page_input');
            if (await pageInput.count() > 0) {
                await pageInput.clear();
                await pageInput.fill(pageNumber.toString());
                await page.keyboard.press('Enter');
                await page.waitForTimeout(2000);
            }
        } catch (error) {
            this.logger?.warn(`导航到第${pageNumber}页失败`, { error: error.message });
        }
    }

    /**
     * Save page data with complete information
     * Integrated from original PlaywrightCrawler.js
     */
    async savePageData(pageNum, list, fullResponse, requestData, pathInfo, parameters) {
        const filename = `${pageNum}.json`;
        const filepath = path.join(pathInfo.encodedPath, filename);

        // Generate complete data structure like original crawler
        const jsonData = {
            fullResponse: fullResponse,
            requestData: requestData, // 保存请求参数用于验证
            crawlInfo: {
                pageNum: pageNum,
                timestamp: new Date().toISOString(),
                combination: {
                    studyPhaseName: parameters.studyPhaseName,
                    subjectName: parameters.subjectName,
                    textbookVersionName: parameters.textbookVersionName,
                    ceciName: parameters.ceciName,
                    catalogName: parameters.catalogName
                },
                recordCount: list.length,
                // 添加路径信息，便于后续验证和调试
                pathInfo: {
                    encodedPath: pathInfo.encodedPath,
                    originalPath: pathInfo.originalPath,
                    pathComponents: pathInfo.pathComponents
                }
            }
        };

        return new Promise((resolve, reject) => {
            fs.writeFile(filepath, JSON.stringify(jsonData, null, 2), 'utf8', (err) => {
                if (err) {
                    this.logger?.error(`❌ 保存第${pageNum}页数据失败`, { error: err.message });
                    reject(err);
                } else {
                    this.logger?.debug(`💾 第${pageNum}页数据保存成功`, {
                        filepath,
                        recordCount: list.length
                    });
                    resolve();
                }
            });
        });
    }

    /**
     * Setup continuous network listening for API response capture
     * This method sets up persistent listening that doesn't block execution
     */
    setupContinuousNetworkListening(page) {
        console.log(`🔧 设置持续网络监听以捕获所有API请求`);

        // Store captured responses for later retrieval
        if (!this.capturedResponses) {
            this.capturedResponses = new Map();
        }

        const responseHandler = async (response) => {
            // Check for API patterns
            const isApiCall = response.url().includes(this.config.apiURL || 'api') ||
                            response.url().includes('/api/') ||
                            response.url().includes('textbookQuery') ||
                            response.url().includes('homeEs');

            if (isApiCall) {
                try {
                    // 获取请求信息
                    const request = response.request();
                    const requestData = request.postData();

                    if (requestData) {
                        try {
                            const requestJson = JSON.parse(requestData);

                            // 验证请求参数是否匹配当前组合
                            if (this.isRequestMatchingCurrentCombination(requestJson)) {
                                const responseData = await response.json();

                                // 检查响应中的403错误
                                if (responseData && responseData.code === 403) {
                                    this.logger?.warn(`🚫 检测到403错误，令牌可能被禁用`);
                                    // Store error response
                                    const pageNum = requestJson.pageNum || 1;
                                    this.capturedResponses.set(pageNum, {
                                        page: pageNum,
                                        url: response.url(),
                                        status: response.status(),
                                        data: responseData,
                                        requestData: requestJson,
                                        timestamp: new Date().toISOString(),
                                        tokenBanned: true
                                    });
                                    return;
                                }

                                // Store successful response
                                const pageNum = requestJson.pageNum || 1;
                                this.capturedResponses.set(pageNum, {
                                    page: pageNum,
                                    url: response.url(),
                                    status: response.status(),
                                    data: responseData,
                                    requestData: requestJson,
                                    timestamp: new Date().toISOString()
                                });

                                this.logger?.debug(`✅ 捕获第${pageNum}页API响应`, {
                                    url: response.url(),
                                    status: response.status(),
                                    itemCount: responseData.data?.list?.length || 0
                                });
                            }
                        } catch (parseError) {
                            this.logger?.warn(`⚠️ 解析请求数据失败: ${parseError.message}`);
                        }
                    }
                } catch (error) {
                    this.logger?.error(`❌ 解析API响应失败: ${error.message}`);
                }
            }
        };

        // Set up the response listener
        page.on('response', responseHandler);

        // Store the handler for potential cleanup
        if (!this.responseHandlers) {
            this.responseHandlers = [];
        }
        this.responseHandlers.push({ page, handler: responseHandler });

        this.logger?.info('✅ 持续网络监听已设置完成');
    }

    /**
     * Get captured response for a specific page
     */
    getCapturedResponse(pageNum) {
        if (!this.capturedResponses) {
            return null;
        }
        return this.capturedResponses.get(pageNum);
    }

    /**
     * Wait for captured response for a specific page
     */
    async waitForCapturedResponse(pageNum, timeout = 30000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const response = this.getCapturedResponse(pageNum);
            if (response) {
                return response;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        this.logger?.warn(`⏰ 等待第${pageNum}页响应超时`);
        return {
            page: pageNum,
            url: null,
            status: 408,
            data: null,
            requestData: null,
            timestamp: new Date().toISOString(),
            timeout: true
        };
    }

    /**
     * Setup network listening for API response capture (original method for compatibility)
     * Integrated from original PlaywrightCrawler.js
     */
    setupNetworkListening(page, pageNum) {
        console.log(`🔧 开始设置网络监听 (页面 ${pageNum})`);
        let responsePromise = null;
        let responseResolver = null;
        let captured = false;

        responsePromise = new Promise((resolve, reject) => {
            responseResolver = resolve;

            const responseHandler = async (response) => {
                if (captured) return; // 已经捕获过了，忽略后续响应

                // Check for API patterns - be more flexible
                const isApiCall = response.url().includes(this.config.apiURL || 'api') ||
                                response.url().includes('/api/') ||
                                response.url().includes('textbookQuery') ||
                                response.url().includes('homeEs');

                if (isApiCall) {
                    try {
                        // 获取请求信息
                        const request = response.request();
                        const requestData = request.postData();

                        if (requestData) {
                            try {
                                const requestJson = JSON.parse(requestData);

                                // 验证请求参数是否匹配当前组合
                                if (this.isRequestMatchingCurrentCombination(requestJson)) {
                                    const responseData = await response.json();

                                    // 检查响应中的403错误
                                    if (responseData && responseData.code === 403) {
                                        this.logger?.warn(`🚫 第${pageNum}页检测到403错误，令牌可能被禁用`);
                                        captured = true;
                                        page.off('response', responseHandler);
                                        resolve({
                                            page: pageNum,
                                            url: response.url(),
                                            status: response.status(),
                                            data: responseData,
                                            requestData: requestJson,
                                            timestamp: new Date().toISOString(),
                                            tokenBanned: true
                                        });
                                        return;
                                    }

                                    captured = true;
                                    page.off('response', responseHandler); // 移除监听器

                                    resolve({
                                        page: pageNum,
                                        url: response.url(),
                                        status: response.status(),
                                        data: responseData,
                                        requestData: requestJson,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } catch (parseError) {
                                this.logger?.warn(`⚠️ 解析请求数据失败: ${parseError.message}`);
                            }
                        }
                    } catch (error) {
                        this.logger?.error(`❌ 解析第${pageNum}页API响应失败: ${error.message}`);
                    }
                }
            };

            page.on('response', responseHandler);

            // 设置超时，避免无限等待
            setTimeout(() => {
                if (!captured) {
                    page.off('response', responseHandler);
                    this.logger?.warn(`⏰ 第${pageNum}页响应捕获超时，跳过`);
                    resolve({
                        page: pageNum,
                        url: null,
                        status: 408,
                        data: null,
                        requestData: null,
                        timestamp: new Date().toISOString(),
                        timeout: true
                    });
                }
            }, 30000); // 30秒超时
        });

        return responsePromise;
    }

    /**
     * Check if request matches current parameter combination
     * Integrated from original PlaywrightCrawler.js
     */
    isRequestMatchingCurrentCombination(requestJson) {
        try {
            // 检查关键参数是否匹配
            const combination = this.currentCombination;

            if (!combination || !requestJson) {
                return false;
            }

            // 检查学段代码
            if (requestJson.studyPhaseCode && requestJson.studyPhaseCode !== combination.studyPhaseCode) {
                return false;
            }

            // 检查学科代码
            if (requestJson.subjectCode && requestJson.subjectCode !== combination.subjectCode) {
                return false;
            }

            // 检查教材版本代码
            if (requestJson.textbookVersionCode && requestJson.textbookVersionCode !== combination.textbookVersionCode) {
                return false;
            }

            // 检查册次代码
            if (requestJson.ceciCode && requestJson.ceciCode !== combination.ceciCode) {
                return false;
            }

            // 检查目录代码
            if (requestJson.catalogCode && requestJson.catalogCode !== combination.catalogCode) {
                return false;
            }

            return true;
        } catch (error) {
            this.logger?.warn(`⚠️ 验证请求匹配失败: ${error.message}`);
            return false;
        }
    }

    /**
     * Get maximum page number from pagination component
     * Integrated from original PlaywrightCrawler.js
     */
    async getMaxPageFromPagination(page) {
        try {
            // 等待分页组件加载
            const pagination = page.locator('ul.ant-pagination');
            await pagination.waitFor({ state: 'visible', timeout: 30000 });

            // 查找所有有title属性的li元素
            const titleElements = await pagination.locator('li[title]').all();

            let maxPage = 1;
            for (const element of titleElements) {
                const titleValue = await element.getAttribute('title');
                if (titleValue) {
                    // 检查title值是否为纯数字
                    const pageNumber = parseInt(titleValue.trim());
                    if (!isNaN(pageNumber) && pageNumber > maxPage) {
                        maxPage = pageNumber;
                    }
                }
            }

            this.logger?.debug(`📄 检测到最大页数: ${maxPage}`);
            return maxPage;

        } catch (error) {
            this.logger?.warn(`⚠️ 从分页获取最大页数失败: ${error.message}`);
            return 1;
        }
    }

    /**
     * Check existing progress by scanning JSON files in output directory
     * 检查输出目录中已有的JSON文件来判断处理进度
     */
    checkExistingProgress(outputPath) {
        try {
            // 检查目录是否存在
            if (!fs.existsSync(outputPath)) {
                this.logger?.debug(`📂 输出目录不存在: ${outputPath}`);
                return {
                    existingPages: [],
                    maxExistingPage: 0,
                    totalFiles: 0
                };
            }

            // 扫描目录中的所有JSON文件
            const files = fs.readdirSync(outputPath);
            const jsonFiles = files.filter(file => file.endsWith('.json'));

            // 提取页码并排序
            const existingPages = [];
            for (const file of jsonFiles) {
                // 文件名格式为 {pageNum}.json
                const match = file.match(/^(\d+)\.json$/);
                if (match) {
                    const pageNum = parseInt(match[1]);
                    if (!isNaN(pageNum)) {
                        existingPages.push(pageNum);
                    }
                }
            }

            existingPages.sort((a, b) => a - b);
            const maxExistingPage = existingPages.length > 0 ? Math.max(...existingPages) : 0;

            this.logger?.debug(`📊 现有进度检查完成`, {
                outputPath,
                totalFiles: existingPages.length,
                maxExistingPage,
                existingPages: existingPages.slice(0, 10) // 只显示前10个页码
            });

            return {
                existingPages,
                maxExistingPage,
                totalFiles: existingPages.length
            };

        } catch (error) {
            this.logger?.warn(`⚠️ 检查现有进度失败: ${error.message}`);
            return {
                existingPages: [],
                maxExistingPage: 0,
                totalFiles: 0
            };
        }
    }

    /**
     * Determine start page based on existing progress and max pages
     * 根据现有进度和最大页数决定从哪一页开始处理
     */
    determineStartPage(outputPath, maxPages) {
        const progress = this.checkExistingProgress(outputPath);

        // 如果已有文件数量等于最大页数，认为已完成
        if (progress.totalFiles >= maxPages) {
            this.logger?.info(`✅ 检测到catalog已完成`, {
                totalFiles: progress.totalFiles,
                maxPages,
                status: 'complete'
            });
            return {
                isComplete: true,
                startPage: null,
                existingFiles: progress.totalFiles,
                maxPages
            };
        }

        // 如果有部分文件，从最大页码+1开始
        if (progress.totalFiles > 0) {
            const startPage = progress.maxExistingPage + 1;
            this.logger?.info(`🔄 检测到部分进度，将从第${startPage}页继续`, {
                existingFiles: progress.totalFiles,
                maxExistingPage: progress.maxExistingPage,
                startPage,
                maxPages,
                status: 'partial'
            });
            return {
                isComplete: false,
                startPage,
                existingFiles: progress.totalFiles,
                maxPages
            };
        }

        // 如果没有文件，从第1页开始
        this.logger?.info(`🆕 未检测到现有进度，将从第1页开始`, {
            maxPages,
            status: 'new'
        });
        return {
            isComplete: false,
            startPage: 1,
            existingFiles: 0,
            maxPages
        };
    }

    /**
     * Early completion check based on params file and existing files
     * 基于params文件和现有文件的早期完成检查
     */
    earlyCompletionCheck(parameters) {
        try {
            // 生成输出路径
            const pathInfo = this.generateOutputPath(parameters);
            const outputPath = pathInfo.encodedPath;

            // 检查参数是否有已知的最大页数
            if (parameters.maxPages && parameters.maxPages > 0) {
                const progress = this.checkExistingProgress(outputPath);

                if (progress.totalFiles >= parameters.maxPages) {
                    this.logger?.info(`🚀 早期检测：Catalog已完成，跳过处理`, {
                        paramKey: this.generateParameterKey(parameters),
                        totalFiles: progress.totalFiles,
                        maxPages: parameters.maxPages,
                        outputPath: pathInfo.originalPath
                    });

                    return {
                        shouldSkip: true,
                        reason: 'Already completed based on params file and existing files',
                        totalFiles: progress.totalFiles,
                        maxPages: parameters.maxPages
                    };
                }
            }

            return {
                shouldSkip: false,
                reason: 'Need to process - either no maxPages info or incomplete'
            };

        } catch (error) {
            this.logger?.warn(`⚠️ 早期完成检查失败: ${error.message}`);
            return {
                shouldSkip: false,
                reason: 'Early check failed, proceeding with normal processing'
            };
        }
    }

    /**
     * Update maxPages in params file for specific parameter
     * 更新params文件中特定参数的最大页数
     */
    async updateParamsMaxPages(parameters, maxPages) {
        try {
            const paramKey = this.generateParameterKey(parameters);

            // 读取当前params文件
            if (!fs.existsSync(this.config.paramsFile)) {
                this.logger?.warn(`⚠️ Params文件不存在: ${this.config.paramsFile}`);
                return false;
            }

            const paramsData = JSON.parse(fs.readFileSync(this.config.paramsFile, 'utf8'));
            let updated = false;

            // 查找并更新对应的参数
            for (let i = 0; i < paramsData.length; i++) {
                const param = paramsData[i];
                const currentParamKey = this.generateParameterKey(param);

                if (currentParamKey === paramKey) {
                    // 只有当maxPages发生变化时才更新
                    if (param.maxPages !== maxPages) {
                        param.maxPages = maxPages;
                        param.lastUpdated = new Date().toISOString();
                        updated = true;

                        this.logger?.info(`📝 更新参数最大页数`, {
                            paramKey,
                            maxPages,
                            originalPath: this.generateOutputPath(param).originalPath
                        });
                    }
                    break;
                }
            }

            // 如果有更新，保存文件
            if (updated) {
                fs.writeFileSync(this.config.paramsFile, JSON.stringify(paramsData, null, 2), 'utf8');
                this.logger?.debug(`💾 Params文件已更新: ${this.config.paramsFile}`);
                return true;
            }

            return false;

        } catch (error) {
            this.logger?.error(`❌ 更新params文件失败: ${error.message}`);
            return false;
        }
    }

    /**
     * Generate parameter key for identification
     * 生成参数标识键
     */
    generateParameterKey(parameters) {
        return [
            parameters.studyPhaseCode || '',
            parameters.subjectCode || '',
            parameters.textbookVersionCode || '',
            parameters.ceciCode || '',
            parameters.catalogCode || ''
        ].join('_');
    }

    /**
     * Handle pagination navigation to specific page
     * Integrated from original PlaywrightCrawler.js
     * 改进：添加输入框清空、重试机制，多次失败后仍认为成功
     */
    async handlePagination(page, targetPage, maxRetries = 3) {
        this.enhancedLogger?.info(`📄 正在导航到第${targetPage}页 (最多重试${maxRetries}次)`);

        // Check if pagination input exists
        const pageInput = page.locator('input.pagination_jump_page_input');
        try {
            await pageInput.waitFor({ state: 'visible', timeout: 30000 });
        } catch (error) {
            this.enhancedLogger?.error('❌ 未找到分页输入框');
            return { success: false, reason: '未找到分页输入框' };
        }

        let lastError = null;
        let lastActualPage = null;

        // 重试逻辑
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                this.enhancedLogger?.debug(`🔄 第${attempt}次尝试导航到第${targetPage}页`);

                // 模拟页面跳转前的行为
                await this.humanBehavior.beforePageNavigation(page);

                // 先清空输入框中已存在的内容
                await this.clearPaginationInput(page, pageInput);

                // 使用人类行为模拟输入页码
                await this.humanBehavior.humanType(page, pageInput, targetPage.toString(), {
                    description: `分页输入框 - 第${targetPage}页 (第${attempt}次尝试)`
                });

                // 验证输入框内容
                const inputValue = await pageInput.inputValue();
                if (inputValue.trim() !== targetPage.toString()) {
                    this.enhancedLogger?.warn(`⚠️ 输入框内容不匹配: 期望"${targetPage}", 实际"${inputValue}"`);

                    if (attempt < maxRetries) {
                        await page.waitForTimeout(1000);
                        continue; // 重试
                    }
                }

                // 模拟按回车键
                await page.waitForTimeout(this.humanBehavior.randomDelay(200, 500));
                await page.keyboard.press('Enter');

                // 等待页面加载，使用随机延迟
                await page.waitForTimeout(this.humanBehavior.randomDelay(2000, 4000));

                // Verify navigation success
                const verificationResult = await this.verifyPaginationNavigation(page, targetPage, attempt);

                if (verificationResult.success) {
                    this.enhancedLogger?.info(`✅ 第${attempt}次尝试成功导航到第${targetPage}页`);
                    return { success: true, actualPage: verificationResult.actualPage, attempts: attempt };
                } else {
                    lastError = verificationResult.reason;
                    lastActualPage = verificationResult.actualPage;

                    if (attempt < maxRetries) {
                        this.enhancedLogger?.warn(`⚠️ 第${attempt}次尝试失败: ${verificationResult.reason}，准备重试`);
                        await page.waitForTimeout(this.humanBehavior.randomDelay(1000, 2000));
                    }
                }

            } catch (error) {
                lastError = error.message;
                this.enhancedLogger?.warn(`❌ 第${attempt}次尝试出现异常: ${error.message}`);

                if (attempt < maxRetries) {
                    await page.waitForTimeout(this.humanBehavior.randomDelay(1000, 2000));
                }
            }
        }

        // 所有重试都失败了，但根据要求仍然认为成功
        this.enhancedLogger?.warn(`⚠️ ${maxRetries}次重试都失败，但仍认为导航成功`, {
            targetPage,
            lastError,
            lastActualPage,
            reason: '多次重试失败后的兜底策略'
        });

        return {
            success: true,
            actualPage: lastActualPage || targetPage,
            attempts: maxRetries,
            forcedSuccess: true,
            originalError: lastError
        };
    }

    /**
     * 清空分页输入框中已存在的内容
     */
    async clearPaginationInput(page, pageInput) {
        try {
            // 获取当前输入框的值
            const currentValue = await pageInput.inputValue();
            if (!currentValue || currentValue.trim() === '') {
                this.enhancedLogger?.debug('📝 输入框已为空，无需清空');
                return;
            }

            this.enhancedLogger?.debug(`🧹 清空输入框现有内容: "${currentValue}"`);

            // 使用最可靠的清空方法：直接设置值为空
            await pageInput.fill('');
            await page.waitForTimeout(200);

            // 验证清空结果
            const afterClearValue = await pageInput.inputValue();
            if (afterClearValue && afterClearValue.trim() !== '') {
                this.enhancedLogger?.warn(`⚠️ fill方法清空失败，尝试其他方法: "${afterClearValue}"`);

                // 备用方法1: 三击选中全部内容后删除
                await pageInput.click({ clickCount: 3 });
                await page.waitForTimeout(100);
                await page.keyboard.press('Delete');
                await page.waitForTimeout(200);

                const secondAttempt = await pageInput.inputValue();
                if (secondAttempt && secondAttempt.trim() !== '') {
                    this.enhancedLogger?.warn(`⚠️ 三击删除也失败，使用强制清空: "${secondAttempt}"`);

                    // 备用方法2: 逐字符删除
                    await pageInput.click();
                    await page.keyboard.press('End'); // 移动到末尾

                    const valueLength = secondAttempt.length;
                    for (let i = 0; i < valueLength; i++) {
                        await page.keyboard.press('Backspace');
                        await page.waitForTimeout(50);
                    }
                    await page.waitForTimeout(200);

                    const finalCheck = await pageInput.inputValue();
                    if (finalCheck && finalCheck.trim() !== '') {
                        this.enhancedLogger?.error(`❌ 所有清空方法都失败，最终内容: "${finalCheck}"`);
                        // 最后的兜底：直接设置空值
                        await pageInput.evaluate(el => el.value = '');
                    } else {
                        this.enhancedLogger?.debug('✅ 逐字符删除成功清空');
                    }
                } else {
                    this.enhancedLogger?.debug('✅ 三击删除成功清空');
                }
            } else {
                this.enhancedLogger?.debug('✅ fill方法成功清空');
            }

        } catch (error) {
            this.enhancedLogger?.warn(`⚠️ 清空输入框时出错: ${error.message}`);
            // 使用最基础的清空方法作为兜底
            try {
                await pageInput.evaluate(el => el.value = '');
                await page.waitForTimeout(100);
                this.enhancedLogger?.debug('✅ 兜底方法(evaluate)成功清空');
            } catch (fallbackError) {
                this.enhancedLogger?.error(`❌ 兜底清空方法也失败: ${fallbackError.message}`);
            }
        }
    }

    /**
     * 验证分页导航结果
     */
    async verifyPaginationNavigation(page, targetPage, attempt) {
        try {
            const activePage = page.locator('.ant-pagination-item-active');
            await activePage.waitFor({ state: 'visible', timeout: 10000 });
            const activePageText = await activePage.textContent();
            const activePageNum = parseInt(activePageText?.trim() || '0');

            if (activePageNum === targetPage) {
                return { success: true, actualPage: activePageNum };
            } else {
                return {
                    success: false,
                    actualPage: activePageNum,
                    reason: `导航不匹配: 期望第${targetPage}页，实际第${activePageNum}页`
                };
            }
        } catch (error) {
            this.enhancedLogger?.warn(`⚠️ 无法验证页面导航 (第${attempt}次尝试): ${error.message}`);

            // 如果无法验证，尝试从输入框获取当前页码
            try {
                const pageInput = page.locator('input.pagination_jump_page_input');
                const inputValue = await pageInput.inputValue();
                const inputPageNum = parseInt(inputValue?.trim() || '0');

                if (inputPageNum === targetPage) {
                    this.enhancedLogger?.debug('📝 通过输入框验证页码匹配');
                    return { success: true, actualPage: inputPageNum };
                } else {
                    return {
                        success: false,
                        actualPage: inputPageNum,
                        reason: `输入框页码不匹配: 期望第${targetPage}页，实际第${inputPageNum}页`
                    };
                }
            } catch (inputError) {
                this.enhancedLogger?.warn(`⚠️ 也无法从输入框验证: ${inputError.message}`);
                return {
                    success: false,
                    actualPage: null,
                    reason: `无法验证导航结果: ${error.message}`
                };
            }
        }
    }

    /**
     * Intelligent delay with randomization
     */
    async intelligentDelay() {
        const delay = this.getRandomDelay();
        this.logger?.debug(`⏱️ 应用智能延迟: ${delay}ms`);
        await this.sleep(delay);
    }

    /**
     * 检测IP是否被封禁
     * 通过不携带token调用API接口来判断是IP封禁还是token封禁
     */
    async checkIpBanStatus() {
        const testApiUrl = 'https://qms.stzy.com/matrix/zw-tk/api/v1/tkUpdateNumberRecord/getUpdateNum';

        try {
            this.logger?.debug('🔍 正在检测IP封禁状态...');

            // 使用https模块发送不携带token的请求
            const response = await this.makeHttpRequest(testApiUrl);

            if (response.data && response.data.code === 403) {
                this.logger?.error('🚫 检测到IP被封禁');
                return {
                    isIpBanned: true,
                    reason: 'IP地址被目标服务器封禁',
                    testApiResponse: response.data
                };
            } else {
                this.logger?.info('✅ IP未被封禁，问题可能是token相关');
                return {
                    isIpBanned: false,
                    reason: 'IP正常，可能是token问题',
                    testApiResponse: response.data
                };
            }

        } catch (error) {
            this.logger?.warn(`⚠️ IP封禁检测失败: ${error.message}`);
            // 如果检测失败，保守处理，假设是token问题
            return {
                isIpBanned: false,
                reason: `IP检测失败: ${error.message}`,
                error: error.message
            };
        }
    }

    /**
     * 发送HTTP请求（不携带token）
     */
    async makeHttpRequest(url) {
        const https = require('https');
        const { URL } = require('url');

        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);

            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': this.config.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive'
                },
                timeout: this.config.requestTimeout || 30000
            };

            const req = https.request(options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({
                            statusCode: res.statusCode,
                            data: jsonData
                        });
                    } catch (parseError) {
                        resolve({
                            statusCode: res.statusCode,
                            data: { error: 'Failed to parse JSON', raw: data }
                        });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            req.end();
        });
    }
}

module.exports = EnhancedPlaywrightCrawler;
